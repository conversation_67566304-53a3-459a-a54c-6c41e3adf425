<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格文本滚动功能演示</title>
    <link rel="stylesheet" href="../dist/lib/index.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: 'Microsoft YaHei', sans-serif;
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        .demo-title {
            text-align: center;
            color: #ffffff;
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: bold;
        }
        
        .demo-subtitle {
            text-align: center;
            color: #b8d4f0;
            font-size: 16px;
            margin-bottom: 30px;
        }
        
        .demo-section {
            margin-bottom: 40px;
        }
        
        .section-title {
            color: #ffffff;
            font-size: 20px;
            margin-bottom: 15px;
            padding-left: 10px;
            border-left: 4px solid #69bfe7;
        }
        
        .controls {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(255, 255, 255, 0.1);
            padding: 10px 15px;
            border-radius: 8px;
            color: #ffffff;
        }
        
        .control-group label {
            font-weight: bold;
            min-width: 80px;
        }
        
        .control-group input, .control-group select {
            padding: 5px 10px;
            border: none;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }
        
        .table-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .feature-list {
            color: #b8d4f0;
            margin: 20px 0;
            padding-left: 20px;
        }
        
        .feature-list li {
            margin-bottom: 8px;
        }
        
        .highlight {
            color: #69bfe7;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">🚀 表格文本滚动功能演示</h1>
        <p class="demo-subtitle">当列宽小于文本长度时，自动启用左右滚动动画</p>
        
        <div class="demo-section">
            <h2 class="section-title">功能特性</h2>
            <ul class="feature-list">
                <li><span class="highlight">智能检测</span>：自动检测文本是否溢出列宽</li>
                <li><span class="highlight">平滑滚动</span>：使用CSS动画实现平滑的左右滚动效果</li>
                <li><span class="highlight">交互友好</span>：鼠标悬停时暂停滚动动画</li>
                <li><span class="highlight">性能优化</span>：只对溢出的文本应用滚动效果</li>
                <li><span class="highlight">响应式</span>：数据变化时自动重新检测和应用滚动</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <h2 class="section-title">控制面板</h2>
            <div class="controls">
                <div class="control-group">
                    <label>文本滚动:</label>
                    <input type="checkbox" id="enableTextScroll" checked>
                </div>
                <div class="control-group">
                    <label>显示边框:</label>
                    <input type="checkbox" id="showBorder" checked>
                </div>
                <div class="control-group">
                    <label>显示行数:</label>
                    <select id="rowCount">
                        <option value="5">5行</option>
                        <option value="8" selected>8行</option>
                        <option value="10">10行</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="section-title">演示表格</h2>
            <div class="table-container">
                <div id="tableContainer"></div>
            </div>
        </div>
    </div>

    <!-- 引入依赖 -->
    <script src="../dist/cdn/vue/vue.min.js"></script>
    <script src="../dist/cdn/element-ui/index.js"></script>
    <script src="../dist/lib/index.umd.min.js"></script>

    <script>
        // 表格配置
        let tableConfig = {
            showHeader: true,
            index: true,
            indexWidth: 60,
            count: 8,
            scroll: false,
            border: true,
            enableTextScroll: true,  // 启用文本滚动
            headerBackground: "#050e18",
            headerColor: "#69bfe7",
            headerFontSize: 14,
            headerTextAlign: "center",
            bodyColor: "#69bfe7",
            bodyFontSize: 12,
            bodyTextAlign: "left",
            nthColor: "rgba(9, 25, 44, 0.8)",
            othColor: "rgba(20, 42, 64, 0.8)",
            column: [
                { 
                    label: "产品ID", 
                    prop: "productId", 
                    width: 80,
                    enableTextScroll: false  // 短文本不需要滚动
                },
                { 
                    label: "产品名称", 
                    prop: "productName", 
                    width: 150,
                    enableTextScroll: true   // 长文本启用滚动
                },
                { 
                    label: "产品描述", 
                    prop: "description", 
                    width: 200,
                    enableTextScroll: true   // 长文本启用滚动
                },
                { 
                    label: "制造商", 
                    prop: "manufacturer", 
                    width: 120,
                    enableTextScroll: true   // 长文本启用滚动
                },
                { 
                    label: "价格", 
                    prop: "price", 
                    width: 80,
                    enableTextScroll: false  // 短文本不需要滚动
                }
            ]
        };

        // 演示数据 - 包含长文本用于测试滚动效果
        const tableData = [
            { 
                productId: "P001", 
                productName: "高性能智能手机Pro Max超长版本名称测试", 
                description: "这是一款具有超强性能和卓越用户体验的智能手机产品，配备最新处理器",
                manufacturer: "科技创新有限责任公司",
                price: "¥8999"
            },
            { 
                productId: "P002", 
                productName: "轻薄笔记本电脑商务办公专业版", 
                description: "专为商务人士设计的轻薄笔记本，拥有长续航和高性能的完美结合",
                manufacturer: "电子设备制造集团",
                price: "¥12999"
            },
            { 
                productId: "P003", 
                productName: "无线蓝牙耳机降噪版旗舰款", 
                description: "采用最新降噪技术的无线蓝牙耳机，为您带来纯净的音乐体验",
                manufacturer: "音频技术研发中心",
                price: "¥1299"
            },
            { 
                productId: "P004", 
                productName: "智能手表健康监测运动版", 
                description: "集健康监测、运动追踪、智能提醒于一体的多功能智能手表",
                manufacturer: "可穿戴设备公司",
                price: "¥2599"
            },
            { 
                productId: "P005", 
                productName: "平板电脑学习娱乐两用款", 
                description: "适合学习和娱乐的高清平板电脑，支持手写笔和键盘配件",
                manufacturer: "教育科技有限公司",
                price: "¥3999"
            },
            { 
                productId: "P006", 
                productName: "游戏机械键盘RGB背光版", 
                description: "专业游戏机械键盘，配备RGB背光和自定义按键功能",
                manufacturer: "游戏外设制造商",
                price: "¥899"
            },
            { 
                productId: "P007", 
                productName: "高清摄像头直播专用设备", 
                description: "专为直播和视频会议设计的高清摄像头，支持自动对焦和美颜",
                manufacturer: "视频设备技术公司",
                price: "¥599"
            },
            { 
                productId: "P008", 
                productName: "无线充电器快充多设备版", 
                description: "支持多设备同时充电的无线充电器，兼容各种品牌手机",
                manufacturer: "充电技术创新企业",
                price: "¥299"
            }
        ];

        // 初始化Vue应用
        const app = new Vue({
            el: '#tableContainer',
            data: {
                tableOption: tableConfig,
                tableData: tableData
            },
            template: `
                <div>
                    <table-component 
                        :option="tableOption" 
                        :data="tableData"
                        style="width: 100%; height: 400px;">
                    </table-component>
                </div>
            `,
            components: {
                'table-component': window.TableComponent || window.Table
            }
        });

        // 控制面板事件处理
        document.getElementById('enableTextScroll').addEventListener('change', function(e) {
            app.tableOption.enableTextScroll = e.target.checked;
        });

        document.getElementById('showBorder').addEventListener('change', function(e) {
            app.tableOption.border = e.target.checked;
        });

        document.getElementById('rowCount').addEventListener('change', function(e) {
            app.tableOption.count = parseInt(e.target.value);
        });

        console.log('文本滚动演示页面已加载');
        console.log('配置:', tableConfig);
        console.log('数据:', tableData);
    </script>
</body>
</html>

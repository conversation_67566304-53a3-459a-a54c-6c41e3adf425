/* @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap'); */

:root {
    --bg-color: #1a1a2e;
    --primary-color: #16213e;
    --secondary-color: #0f3460;
    --font-color: #e94560;
    --text-color: #dcdcdc;
    --border-color: #0f3460;
    --running-color: #2ecc71;
    --idle-color: #3498db;
    --alarm-color: #e74c3c;
    --arrow-color: #537895;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans SC', sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    overflow: hidden; /* Prevent scrollbars */
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    padding: 20px 40px 20px 20px;
}

header h1 {
    text-align: center;
    color: var(--font-color);
    font-size: 2.5rem;
    margin-bottom: 20px;
    text-shadow: 0 0 10px var(--font-color);
}

.device-layout {
    position: relative;
    flex-grow: 1;
    background-color: var(--primary-color);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    background-image: radial-gradient(circle, var(--secondary-color) 1px, transparent 1px);
    background-size: 20px 20px;
    overflow: hidden; /* Ensure no scrollbars */
    margin-left: 20px;
}

#zoom-container {
    position: relative;
    transition: transform 0.3s ease-in-out;
    transform-origin: top left;
}

.device {
    position: absolute;
    width: 120px;
    height: 70px;
    background-color: var(--secondary-color);
    border: 2px solid var(--idle-color);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
}

.device:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
}

.device.running {
    border-color: var(--running-color);
}

.device.alarm {
    border-color: var(--alarm-color);
    animation: pulse-alarm 1s infinite;
}

@keyframes pulse-alarm {
    0% {
        box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
    }
}

.device-name {
    font-weight: 500;
    font-size: 0.9rem;
}

.device-icons {
    position: absolute;
    top: 5px;
    right: 5px;
    display: flex;
    gap: 5px;
}

.device-icons i {
    font-size: 0.8rem;
    opacity: 0.7;
}

.device-icons .fa-video {
    color: var(--idle-color);
}

.device-icons .fa-star {
    color: #f1c40f;
}

.device.alarm .fa-triangle-exclamation {
    color: var(--alarm-color);
    display: inline-block !important;
}

.connector {
    position: absolute;
    stroke: var(--arrow-color);
    stroke-width: 2;
    fill: none;
    marker-end: url(#arrowhead);
}

.flow-dot {
    position: absolute;
    width: 8px;
    height: 8px;
    background-color: var(--running-color);
    border-radius: 50%;
    box-shadow: 0 0 5px var(--running-color), 0 0 10px var(--running-color);
    opacity: 0;
    animation: flow 4s linear infinite;
}

@keyframes flow {
    0% {
        opacity: 1;
        offset-distance: 0%;
    }
    90% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        offset-distance: 100%;
    }
}

/* Modal Styles */
/* .modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: var(--primary-color);
    margin: 15% auto;
    padding: 20px;
    border: 1px solid var(--border-color);
    width: 80%;
    max-width: 600px;
    border-radius: 10px;
    position: relative;
    animation: slide-in 0.5s ease-out;
}

.modal-content.camera-view {
    max-width: 850px;
} */

@keyframes slide-in {
    from { top: -100px; opacity: 0; }
    to { top: 0; opacity: 1; }
}

.close-button {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-button:hover,
.close-button:focus {
    color: var(--text-color);
    text-decoration: none;
}

#modal-title, #camera-title {
    color: var(--font-color);
    margin-bottom: 20px;
}

.tabs {
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 20px;
}

.tab-link {
    background: none;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    color: var(--text-color);
    font-size: 1rem;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.tab-link.active, .tab-link:hover {
    opacity: 1;
    border-bottom: 2px solid var(--font-color);
}

.tab-content {
    display: none;
    animation: fade-in 0.5s;
}

.tab-content.active {
    display: block;
}

@keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
}

.tab-content p {
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.tab-content p span {
    color: var(--running-color);
    font-weight: 500;
}

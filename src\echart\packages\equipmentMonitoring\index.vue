<template>
  <div :class="[b(), className]" :style="styleSizeName">
    <!-- {{ styleSizeName }} -->
    <equipment-monitoring
      :styleSizeName="styleSizeName"
      :option="option"
      :data="deviceData"
      :data-type="dataType"
      :data-method="dataMethod"
      :url="url"
      :data-query="dataQuery"
      :data-header="dataHeader"
      :data-formatter="dataFormatter"
      :data-set="dataSet"
      :time="time"
    />
  </div>
</template>

<script>
import create from "../../create";
import EquipmentMonitoring from '@/components/equipmentMonitoring/index.vue'

export default create({
  name: 'equipmentMonitoring',
  components: {
    EquipmentMonitoring
  },
  props: {
    option: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    // 处理设备数据
    deviceData() {
      if (Array.isArray(this.dataChart)) {
        return this.dataChart;
      } else if (this.dataChart && typeof this.dataChart === 'object') {
        return [this.dataChart];
      } else {
        return [];
      }
    }
  }
})
</script>

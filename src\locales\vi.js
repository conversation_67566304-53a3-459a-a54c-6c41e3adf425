//zh.js
export default {
    other: {
        'ConfirmBtn': '确定 Xác nhận',
        'CancelBtn': '取消 Hủy bỏ',
    },
    message: {
        "PleaseEnterURL": "请输入 URL Vui lòng nhập URL",
        "PleaseEnterUserName": "请输入用户名 Vui lòng nhập tên người dùng",
        "PleaseEnterDatabaseName": "请输入数据库名称 Vui lòng nhập tên cơ sở dữ liệu",
        "PleaseEnterPortNumber": "请输入端口号 Vui lòng nhập số cổng",
        "PleaseEnterHostName": "请输入主机名 Vui lòng nhập tên_HOST",
        "PleaseEnterDataSourceName": "请输入数据源名称 Vui lòng nhập tên nguồn dữ liệu",
        "PleaseSelectDataSourceType": "请选择数据源类型 Vui lòng chọn loại nguồn dữ liệu",
        "PleaseSelect": "请选择 Vui lòng chọn",
        "PleaseEnterTemplateName": "请输入模板名称 Vui lòng nhập tên mẫu",
        "PleaseSelectType": "请选择类型 Vui lòng chọn loại",
        "PleaseEnterMapName": "请输入地图名称 Vui lòng nhập tên bản đồ",
        "PleaseEnterConnectionAddress": "请输入连接地址 Vui lòng nhập địa chỉ kết nối",
        "PleaseEnterDriverClass": "请输入驱动类 Vui lòng nhập lớp động cơ",
        "PleaseSelectComponentType": "请选择组件类型 Vui lòng chọn loại thành phần",
        "ComponentPreview": "组件预览 Xem trước thành phần",
        "ComponentData": "组件数据 Dữ liệu thành phần",
        "PleaseEnterComponentName": "请输入组件名称 Vui lòng nhập tên thành phần",
        "PleaseEnterNumber": "请输入编号 Vui lòng nhập sốSerial",
        "PleaseEnterName": "请输入名称 Vui lòng nhập tên",
        "PleaseEnterBigScreenName": "请输入大屏名称 Vui lòng nhập tên màn hình lớn",
        "PleaseSelectTemplate": "请选择模板 Vui lòng chọn mẫu",

        'OperationSuccessful': "操作成功 Thao tác thành công",
        'SavedSuccessfully': "保存成功 Lưu thành công",
        'confirmButtonText': "确定 Xác nhận",
        'cancelButtonText': "取消 Hủy bỏ",
        'AddedSuccessfully': "添加成功 Thêm thành công",
        'PleaseDeleteTheLastTabFirst': "请先删除最后一个选项卡 Vui lòng xóa thẻ cuối cùng trước",
        'TheTimeCannotBeLessThan1000': "时间不能小于1000!! Thời gian không thể nhỏ hơn 1000!!",
        'CopiedSuccessfully': "复制成功 Sao chép thành công",
        'CopyFailed': "复制失败 Sao chép thất bại",
        'ImportedSuccessfully': "导入成功 Nhập vào thành công",
        'RequestForDataWasSuccessful': "请求数据成功 Yêu cầu dữ liệu thành công",
        'SuccessfullyCopiedTheComponent': "复制组件成功 Sao chép thành phần thành công",
        'SuccessfullyPastedTheComponent': "粘贴组件成功 Dán thành phần thành công",
        'ConfigSavedSuccessfully': "大屏配置保存成功 Lưu cấu hình màn hình lớn thành công",
        'ConfigSavingFailed': "大屏配置保存失败，请检查服务端配置 Lưu cấu hình màn hình lớn thất bại, vui lòng kiểm tra cấu hình phía máy chủ",
        'PictureExportedSuccessfully': "图片导出成功 Xuất ảnh thành công",
        'DataFormatIsIncorrect': "数据格式有误 Định dạng dữ liệu sai",
        'DataImportedSuccessfully': "数据导入成功 Nhập dữ liệu thành công",
        'ErrorInImportingData': "导入数据错误 Lỗi khi nhập dữ liệu",
        'LinkCopiedSuccessfully': "链接复制成功 Sao chép liên kết thành công",
        'DashboardSharedSuccessfully': "大屏分享成功 Chia sẻ màn hình lớn thành công",
        'DeletedSuccessfully': "删除成功 Xóa thành công",
        'PermanentDeletion': "此操作将永久删除，是否继续？ Hành động này sẽ xóa vĩnh viễn, có tiếp tục không?",
        'Prompt': "提示 Thông báo",
        'ModifiedSuccessfully': "修改成功 Sửa đổi thành công",
        'NewlyAddedSuccessfully': "新增成功 Thêm mới thành công",
        'ConnectedSuccessfully': "连接成功 Kết nối thành công",
        'ConnectionFailed': "连接失败，请检查相关配置参数 Kết nối thất bại, vui lòng kiểm tra các tham số cấu hình liên quan",
        'SharedSuccessfully': "分享成功 Chia sẻ thành công",
        'DataRefreshedSuccessfully': "数据刷新成功 Tải lại dữ liệu thành công",
        'VerificationCodeCannotBeEmpty': "验证码不能为空 Mã xác nhận không thể trống",
        'VerificationCodeIsIncorrect': "验证码不正确 Mã xác nhận không đúng",
        'CombineTheSelectedLayers': "是否组合所选择的图层 Có muốn hợp thành các lớp đã chọn không",
        'BatchDeleteTheSelectedLayers': "是否批量删除所选图层 Có muốn xóa hàng loạt các lớp đã chọn không",
        'ExportTheLargeScreenPicture': "是否导出大屏图片 Có muốn xuất ảnh màn hình lớn không",
        'DeleteTheSelectedLayers': "是否删除所选图层 Có muốn xóa các lớp đã chọn không",
        'ConfirmToDeleteTheSelectedSata': "确定将选择数据删除 Xác nhận xóa dữ liệu đã chọn",
        'DisassembleTheLayers': "是否解散图层 Có muốn giải phóng lớp không"
    },
    rules: {
        "PleaseEnterBigScreenName": "请输入大屏名称 Vui lòng nhập tên màn hình lớn",
        "PleaseSelectTemplate": "请选择模板 Vui lòng chọn mẫu",
        "PleaseSelectDataSource": "请选择数据源 Vui lòng chọn nguồn dữ liệu",
        "PleaseEnterDataSourceName": "请输入数据源名称 Vui lòng nhập tên nguồn dữ liệu",
        "PleaseEnterNumber": "请输入编号 Vui lòng nhập sốSerial",
        "PleaseEnterName": "请输入名称 Vui lòng nhập tên",
        "PleaseEnterDriverClass": "请输入驱动类 Vui lòng nhập lớp động cơ",
        "PleaseEnterUserName": "请输入用户名 Vui lòng nhập tên người dùng",
        "PleaseEnterPassword": "请输入密码 Vui lòng nhập mật khẩu",
        "PleaseEnterConnectionAddress": "请输入连接地址 Vui lòng nhập địa chỉ kết nối",
        "PleaseSelect": "请选择 Vui lòng chọn",
        "PleaseSelectType": "请选择类型 Vui lòng chọn loại",
        "PleaseEnterTemplateName": "请输入模板名称 Vui lòng nhập tên mẫu",
        "PleaseEnterMapName": "请输入地图名字 Vui lòng nhập tên bản đồ",
        "PleaseSelectDataSourceType": "请选择数据源类型 Vui lòng chọn loại nguồn dữ liệu",
        "PleaseEnterDataSourceName": "请输入数据源名称 Vui lòng nhập tên nguồn dữ liệu",
        "PleaseEnterHostName": "请输入主机名 Vui lòng nhập tên_HOST",
        "PleaseEnterPortNumber": "请输入端口号 Vui lòng nhập số cổng",
        "PleaseEnterDatabaseName": "请输入数据库名称 Vui lòng nhập tên cơ sở dữ liệu",
        "PleaseEnterUserName": "请输入用户名 Vui lòng nhập tên người dùng",
        "PleaseEnterPassword": "请输入密码 Vui lòng nhập mật khẩu",
        "PleaseSelectDataSourceType": "请选择数据源类型 Vui lòng chọn loại nguồn dữ liệu",
        "PleaseEnterDataSourceName": "请输入数据源名称 Vui lòng nhập tên nguồn dữ liệu",
        "PleaseEnterURL": "请输入 URL Vui lòng nhập URL"
    },
    page: {
        largescreentemplate: {
            "Add": "新增 Thêm mới",
            "Import": "导入 Nhập vào",
            "Export": "导出 Xuất ra",
            "Synchronize": "同步 Đồng bộ hóa",
            "Keyword": "关键词 Từ khóa",
            "Query": "查询 Tìm kiếm",
            "Reset": "重置 Đặt lại",
            "Edit": "编辑 Sửa",
            "Delete": "删除 Xóa",
            "SerialNumber": "序号 SốSerial",
            "Operation": "操作 Thao tác",
            "PleaseSelectLabel": "请选择标签 Vui lòng chọn thẻ",
            "TemplateName": "模板名称 Tên mẫu",
            "PleaseSearchforTemplateName": "请搜索模板名称 Vui lòng tìm kiếm tên mẫu",
            "CreateNewTemplate": "新建模板 Tạo mẫu mới",
            "Design": "设计 Thiết kế",
            "Preview": "预览 Xem trước",
            "Copy": "复制 Sao chép",
            "PleaseEnterTemplateName": "请输入模板名称 Vui lòng nhập tên mẫu",
            "TemplateTag": "模板标签 Thẻ mẫu",
            "BigScreenWidth": "大屏宽度 Chiều rộng màn hình lớn",
            'BigScreenHeight': "大屏高度 Chiều cao màn hình lớn",
            "PleaseEnterBigScreenWidth": "请输入大屏宽度 Vui lòng nhập chiều rộng màn hình lớn",
            "PleaseEnterBigScreenHeight": "请输入大屏高度 Vui lòng nhập chiều cao màn hình lớn",
            "Cancel": "取消 Hủy bỏ",
            "Confirm": "确认 Xác nhận",
            'TagCategory': "标签类别 Loại thẻ",
        },
        create: {
            "BigScreenDesign": "大屏设计 Thiết kế màn hình lớn",
            "Cancel": "取消 Hủy bỏ",
            "Confirm": "确定 Xác nhận",
            "BigScreenName": "大屏名称 Tên màn hình lớn",
            "BelongingGroup": "所属分组 Nhóm thuộc",
            "PleaseSelectBelongingGroup": "请选择所属分组 Vui lòng chọn nhóm thuộc",
            "BigScreenSize": "大屏尺寸 Kích thước màn hình lớn",
            "WidthHeight": "宽*高 Rộng * Cao",
            "BigScreenPassword": "大屏密码 Mật khẩu màn hình lớn",
            "SelecttheFollowingMethodsforCreation": "选择下面的方式进行创建 Chọn cách tạo dưới đây",
            "TemplateName": "模板名称 Tên mẫu",
            "PleaseSearchforTemplateName": "请搜索模板名称 Vui lòng tìm kiếm tên mẫu",
            "Query": "查询 Tìm kiếm",
            "Reset": "重置 Đặt lại",
            "BlankTemplate": "空白模板 Mẫu trống",
            "UpdateKanbanList": "更新看板列表 Cập nhật danh sách bảng điều khiển"
        },
        mapManagement: {
            "Add": "新增 Thêm mới",
            "Import": "导入 Nhập vào",
            "Export": "导出 Xuất ra",
            "Synchronize": "同步 Đồng bộ hóa",
            "Keyword": "关键词 Từ khóa",
            "Query": "查询 Tìm kiếm",
            "Reset": "重置 Đặt lại",
            "Edit": "编辑 Sửa",
            "Delete": "删除 Xóa",
            "SerialNumber": "序号 SốSerial",
            "Operation": "操作 Thao tác",
            "MapName": "地图名称 Tên bản đồ",
            "PleaseEnterMapName": "请输入地图名称 Vui lòng nhập tên bản đồ",
            "AddMoreMaps": "添加更多地图 Thêm nhiều bản đồ",
            "MapData": "地图数据 Dữ liệu bản đồ",
            "PleaseEnterMapData": "请输入地图数据 Vui lòng nhập dữ liệu bản đồ",
            "Cancel": "取消 Hủy bỏ",
            "Confirm": "确定 Xác nhận",
            "MapName": "地图名字 Tên bản đồ",
            "PleaseEnterMapName": "请输入地图名字 Vui lòng nhập tên bản đồ",
            "MapData": "地图数据 Dữ liệu bản đồ",
            "PleaseEnterMapData": "请输入地图数据 Vui lòng nhập dữ liệu bản đồ",
            'AddMoreMaps': "添加更多地图 Thêm nhiều bản đồ",
            'CreateMap': "新建地图 Tạo bản đồ mới",
            'EditMap': "编辑地图 Sửa bản đồ"

        },
        filemanagement: {
            "Add": "新增 Thêm mới",
            "Import": "导入 Nhập vào",
            "Export": "导出 Xuất ra",
            "Synchronize": "同步 Đồng bộ hóa",
            "Keyword": "关键词 Từ khóa",
            "Query": "查询 Tìm kiếm",
            "Reset": "重置 Đặt lại",
            "Edit": "编辑 Sửa",
            "Delete": "删除 Xóa",
            "SerialNumber": "序号 SốSerial",
            "Operation": "操作 Thao tác",
            "PictureName": "图片名称 Tên ảnh",
            "PictureType": "图片类型 Loại ảnh",
            "PicturePath": "图片路径 Đường dẫn ảnh",
            "PicturePreview": "图片预览 Xem trước ảnh",
            "Loading": "拼命加载中 Đang tải dữ liệu",
            "DragTheFileHere": "将文件拖到此处 Kéo tệp đến đây",
            "ClickToUpload": "点击上传 Nhấp để tải lên",
            'Create': "新建 Tạo mới",
        },
        components: {
            "Add": "新增 Thêm mới",
            "PleaseEnterName": "请输入名称 Vui lòng nhập tên",
            "Preview": "预览 Xem trước",
            "Select": "选择 Chọn",
            "Edit": "编辑 Sửa",
            "Delete": "删除 Xóa",
            "NoDataAvailable": "暂无数据 Không có dữ liệu",
            "EditComponent": "编辑组件 Sửa thành phần",
            "EnlargeEditor": "放大编辑器 Phóng to trình soạn thảo",
            "OfficialComponentLibrary": "官方组件库 Thư viện thành phần chính thức",
            "ThirdPartyComponentLibrary": "三方组件库 Thư viện thành phần bên thứ ba",
            "ComponentName": "组件名称 Tên thành phần",
            "PleaseEnterComponentName": "请输入组件名称 Vui lòng nhập tên thành phần",
            "ComponentType": "组件类型 Loại thành phần",
            "PleaseSelectComponentType": "请选择组件类型 Vui lòng chọn loại thành phần",
            "ComponentData": "组件数据 Dữ liệu thành phần",
            "ComponentPreview": "组件预览 Xem trước thành phần"

        },
        classification: {
            "Add": "新增 Thêm mới",
            "Import": "导入 Nhập vào",
            "Export": "导出 Xuất ra",
            "Synchronize": "同步 Đồng bộ hóa",
            "Keyword": "关键词 Từ khóa",
            "Query": "查询 Tìm kiếm",
            "Reset": "重置 Đặt lại",
            "Edit": "编辑 Sửa",
            "Delete": "删除 Xóa",
            "SerialNumber": "序号 SốSerial",
            "Operation": "操作 Thao tác",
            "GroupingName": "分组名称 Tên nhóm",
            "GroupingDescription": "分组描述 Mô tả nhóm",
            "EditSave": "编辑 & 保存 Sửa & Lưu",
            "AddSave": "新增 & 保存 Thêm mới & Lưu",
            "ParentGrouping": "父级分组 Nhóm cha",
            "PleaseEnterGroupingName": "请输入分组名称 Vui lòng nhập tên nhóm",
            "PleaseEnterGroupingDescription": "请输入分组描述 Vui lòng nhập mô tả nhóm",
            "Cancel": "取消 Hủy bỏ",
            "Confirm": "确定 Xác nhận"

        },
        kanbanLabel: {
            "Add": "新增 Thêm mới",
            "Import": "导入 Nhập vào",
            "Export": "导出 Xuất ra",
            "Synchronize": "同步 Đồng bộ hóa",
            "Keyword": "关键词 Từ khóa",
            "Query": "查询 Tìm kiếm",
            "Reset": "重置 Đặt lại",
            "Edit": "编辑 Sửa",
            "Delete": "删除 Xóa",
            "AddTemplateTag": "添加模板标签 Thêm thẻ mẫu",
            "EditTemplateTag": "编辑模板标签 Sửa thẻ mẫu",
            "SerialNumber": "序号 SốSerial",
            "LabelCode": "标签编号 Mã thẻ",
            "LabelName": "标签名称 Tên thẻ",
            "LabelRemarks": "标签备注 Ghi chú thẻ",
            "Operation": "操作 Thao tác",
            "PleaseEnterLabelNumber": "请输入标签编号 Vui lòng nhập mã thẻ",
            "PleaseEnterLabelName": "请输入标签名称 Vui lòng nhập tên thẻ",
            "PleaseEnterLabelName": "请输入标签名称 Vui lòng nhập tên thẻ",
            "Cancel": "取消 Hủy bỏ",
            "Confirm": "确定 Xác nhận"
        },
        largescreendesign: {
            "ShareBigScreen": "分享大屏 Chia sẻ màn hình lớn",
            "PleaseSelecttheBelongingTemplate": "请选择所属模板 Vui lòng chọn mẫu thuộc",
            "ShareLink": "分享链接 Liên kết chia sẻ",
            "CopyLink": "复制链接 Sao chép liên kết",
            "SharePassword": "分享密码 Mật khẩu chia sẻ",
            KanbanGrouping: "看板分组 Nhóm bảng điều khiển",
            Selectthefollowingmethodstocreate: "选择下面的方式进行创建 Chọn cách tạo dưới đây",
            Import: "导入 Nhập vào",
            Export: "导出 Xuất ra",
            Synchronize: "同步 Đồng bộ hóa",
            KanbanName: "看板名称 Tên bảng điều khiển",
            'SearchTemplateName': "请搜索模板名称 Vui lòng tìm kiếm tên mẫu",
            'Search': "查询 Tìm kiếm",
            'Reset': "重置 Đặt lại",
            "CreateNewBigScreen": "新建大屏 Tạo màn hình lớn mới",
            "Preview": "预览 Xem trước",
            "Share": "分享 Chia sẻ",
            "Design": "设计 Thiết kế",
            "Delete": "删除 Xóa",
            "Edit": "编辑 Sửa",
            "Copy": "复制 Sao chép",
            "AddTemplate": "添加模板 Thêm mẫu",
            "BigScreenName": "大屏名称 Tên màn hình lớn",
            "PleaseEnterBigScreenName": "请输入大屏名称 Vui lòng nhập tên màn hình lớn",
            "BelongingGroup": "所属分组 Nhóm thuộc",
            "PleaseSelectBelongingGroup": "请选择所属分组 Vui lòng chọn nhóm thuộc",
            "BigScreenWidth": "大屏宽度 Chiều rộng màn hình lớn",
            "PleaseEnterBigScreenWidth": "请输入大屏宽度 Vui lòng nhập chiều rộng màn hình lớn",
            "BigScreenHeight": "大屏高度 Chiều cao màn hình lớn",
            "PleaseEnterBigScreenHeight": "请输入大屏高度 Vui lòng nhập chiều cao màn hình lớn",
            "Cancel": "取消 Hủy bỏ",
            "Confirm": "确认 Xác nhận",
            "BelongingLabel": "所属标签 Thẻ thuộc",
            "PleaseSelectLabel": "请选择标签 Vui lòng chọn thẻ"
        },
        top: {
            'search': "搜索组件 Tìm kiếm thành phần"
        },
        header: {
            LargeScreenName: "大屏名称 Tên màn hình lớn",
            TemplateName: "模板名称 Tên mẫu",
            Preview: "预览 Xem trước",
            SaveCover: "保存封面 Lưu bìa",
            SaveConfiguration: "保存配置 Lưu cấu hình",
            Import: "导入 Nhập vào",
            Export: "导出 Xuất ra",
            Undo: "撤销 Hủy tác vụ trước",
            'Tip': "提示 Gợi ý",
            'WhetherToExportTheLargeScreenPicture': "是否导出大屏图片 Có muốn xuất ảnh màn hình lớn không",
            'Prompt': "提示 Thông báo",
            'Confirm': "确定 Xác nhận",
            'Cancel': "取消 Hủy bỏ",
            'PictureExportSuccessful': "图片导出成功 Xuất ảnh thành công",
            'PleaseCheckTheServerConfiguration': "大屏配置保存失败，请检查服务端配置 Lưu cấu hình màn hình lớn thất bại, vui lòng kiểm tra cấu hình phía máy chủ",
            'LargeScreenConfigurationSavedSuccessfully': "大屏配置保存成功 Lưu cấu hình màn hình lớn thành công",
            'UpdateDashboardList': "更新看板列表 Cập nhật danh sách bảng điều khiển",
            'SavingConfiguration': "正在保存配置，请稍后 Đang lưu cấu hình, vui lòng đợi",
            'LargeScreenConfigurationCoverSavedSuccessfully': "大屏配置封面保存成功 Lưu bìa cấu hình màn hình lớn thành công",
            'LargeScreenConfigurationCoverFailedToBeSaved': "大屏配置封面保存失败，请检查服务端配置 Lưu bìa cấu hình màn hình lớn thất bại, vui lòng kiểm tra cấu hình phía máy chủ",
            'SavingTheConfigurationCover': "正在保存配置封面，请稍后 Đang lưu bìa cấu hình, vui lòng đợi",
        },
        build: {
            'PromptEvent': "提示事件 Sự kiện thông báo",
            'TitleEvent': "标题事件 Sự kiện tiêu đề",
            'DataSourceEdit': "数据源修改 Sửa nguồn dữ liệu",
            'SystemColor': "系统配色 Màu hệ thống",
            'RequestHeader': "请求头Headers Trang header Yêu cầu",
            'RequestConfiguration': "请求配置 Cấu hình yêu cầu",
            'Grouping': "分组 Nhóm",
            'Delete': "删除 Xóa",
            'Filter': "过滤器 Bộ lọc",
            'ResponseData': "响应数据 Dữ liệu phản hồi",
            'EditFilter': "编辑过滤器 Sửa bộ lọc",
            'DOCOnLine': "在线文档 Tài liệu trực tuyến",
            'ClickToView': "点击查看 Nhấp để xem",
            'ModuleName': "模块名称 Tên mô-đun",
            'ConfigurationList': "配置列表 Danh sách cấu hình",
            'Subclass': "子类 Tên lớp con",
            'ParameterName': "参数名称 Tên tham số",
            'MappingFields': "映射字段 Trường ánh xạ",
            'Defaultvalue': "默认为value Mặc định là value",
            'ClickEvent': "点击事件 Sự kiện nhấp",
            'Event': "事件 Sự kiện",
            'basis': "基础 Cơ sở",
            'requestData': "请求数据 Dữ liệu yêu cầu",
            'Component': "组件 Thành phần",
            'Layer': "图层 Lớp",
            'ComponentConfiguration': "组件配置 Cấu hình thành phần",
            'Data': "数据 Dữ liệu",
            'DataType': "数据类型 Loại dữ liệu",
            'SQLStatement': "SQL语句 Câu lệnh SQL",
            'InterfaceAddress': "接口地址 Địa chỉ giao diện",
            'RequestMethod': "请求方式 Phương thức yêu cầu",
            'WSAddress': "WS地址 Địa chỉ WS",
            'DatasetSelection': "数据集选择 Chọn tập dữ liệu",
            'DataSourceSelection': "数据源选择 Chọn nguồn dữ liệu",
            'RefreshTime': "刷新时间(毫秒) Thời gian làm mới (milisec)",
            'ParameterConfiguration': "参数配置 Cấu hình tham số",
            'GlobalDataset': "全局数据集 Tập dữ liệu toàn cầu",
            'MoreSettings': "更多设置 Cấu hình thêm",
            'LargeScreenConfiguration': "大屏配置 Cấu hình màn hình lớn",
            'Basic': "Basic Cơ bản",
            'HorizontalAlignment': "水平对齐方式 Căn chỉnh ngang",
            'LeftAlignment': "左对齐 Căn trái",
            'CenterAlignment': "居中对齐 Căn giữa",
            'RightAlignment': "右对齐 Căn phải",
            'VerticalAlignment': "垂直对齐方式 Căn chỉnh dọc",
            'TopAlignment': "顶对齐 Căn trên",
            'MiddleAlignment': "中部对齐 Căn giữa",
            'BottomAlignment': "底对齐 Căn dưới",
            'Group': "Group Nhóm",
            'Delete': "Delete Xóa",
            'XAxisModel': "X轴模型 Mô hình trục X",
            'YAxisModel': "Y轴模型 Mô hình trục Y",
            'MultiYAxisModel': "多Y轴模型 Mô hình nhiều trục Y",
            'LayerName': "图层名称 Tên lớp",
            'Hide': "隐藏 Ẩn",
            'Lock': "锁定 Khóa",
            'SystemColorScheme': "系统配色 Bảng màu hệ thống",
            'LargeScreenName': "大屏名称 Tên màn hình lớn",
            'LargeScreenWidth': "大屏宽度 Chiều rộng màn hình lớn",
            'LargeScreenHeight': "大屏高度 Chiều cao màn hình lớn",
            'LargeScreenIntroduction': "大屏简介 Giới thiệu màn hình lớn",
            'PleaseEnterTheLargeScreenIntroduction': "请输入大屏简介 Vui lòng nhập giới thiệu màn hình lớn",
            'BackgroundColor': "背景颜色 Màu nền",
            'BackgroundPicture': "背景图片 Hình nền",
            'RefreshPageTime': "刷新页面时间 Thời gian làm mới trang",
            'GlobalData': "全局数据源 Nguồn dữ liệu toàn cầu",
            'DataSource': "数据源 Nguồn dữ liệu",
            'Dataset': "数据集 Tập dữ liệu",
            'FirstLoad': "首次获取将在(ms)后 Lấy lần đầu sẽ sau (ms)",
            'SelectionTime': "选择时间 Thời gian chọn",
            'PreviewMode': "预览方式 Phương thức xem trước",
            'XAxisFilling': "X轴铺满 Điền dày trục X",
            'YAxisFilling': "Y轴铺满 Điền dày trục Y",
            'ForceStretchingThePicture': "强行拉伸画面 Kép cường dãn ra hình ảnh",
            'MoreSettings': "更多设置 Cấu hình thêm",
            'Interaction': "交互 Hoạt động tương tác",
            'Parameter': "参数 Tham số",
            'SerialNumber': "序号 SốSerial",
            'Configuration': "配置 Cấu hình",
            'EntryAnimation': "进入动画 Hoạt ảnh nhập",
            'ClickToViewAnimationType': "点击查看动画类型 Nhấp để xem loại hoạt ảnh",
            'Position': "位置 Vị trí",
            'Size': "尺寸 Kích thước",
            'Font': "字体 Chữ體",
            'Perspective': "透视 Tầm nhìn",
            'Scale': "缩放 Tỷ lệ",
            'Opacity': "透明度 Mức trong suốt",
            'XRotationDegree': "X旋转度 Độ xoay X",
            'YRotationDegree': "Y旋转度 Độ xoay Y",
            'ZRotationDegree': "Z旋转度 Độ xoay Z",
            'MoreSettings': "更多设置 Cấu hình thêm",
            'GlobalRequestAddress': "全局请求地址 Địa chỉ yêu cầu toàn cầu",
            'GlobalRequestParameters': "全局请求参数 Tham số yêu cầu toàn cầu",
            'GlobalRequestHeader': "全局请求头 Trang header yêu cầu toàn cầu",
            'LargeScreenWatermark': "大屏水印 Chữ nước màn hình lớn",
            'Content': "内容 Nội dung",
            'Size': "大小 Kích thước",
            'Color': "颜色 Màu sắc",
            'Angle': "角度 Góc",
            'DataValue': "数据值 Giá trị dữ liệu",
            'EditDataValue': "编辑数据值 Sửa giá trị dữ liệu",
            'ImportData': "导入数据（Excel） Nhập dữ liệu (Excel)",
            'JustWriteItAsAFunctionToReturnSQL': "若要获取变量，直接写成函数返回SQL语句即可 Nếu muốn lấy biến, hãy viết trực tiếp thành hàm trả về câu lệnh SQL",
            'RequestParameters_BODY': "请求参数（Body） Tham số yêu cầu (Body)",
            'RequestParameters': "请求参数 Tham số yêu cầu",
            'Filter': "过滤器 Bộ lọc",
            'Edit': "编辑 Sửa",
            'Close': "关闭 Đóng",
            'SaveAndRequest': "保存并请求 Lưu và yêu cầu",
            'FilterAndRequest': "过滤并请求 Lọc và yêu cầu",
            'OriginalRequest': "原始请求 Yêu cầu ban đầu",
            'ResponseData': "响应数据 Dữ liệu phản hồi",
            'DataSourceModification': "数据源修改 Sửa đổi nguồn dữ liệu"

        },

    },
    components: {
        imgList: {
            'HoverStop': "悬停是否停止 Dừng khi đ hovering",
            'ScrollingTime': "滚动时间 Thời gian cuộn",
            'Direction': "方向 Hướng",
            'TabTime': "选项卡时间 Thời gian thẻ",
            'Autoplay': "自动播放 Chơi tự động",
            'CarouselTime': "走马灯时间 Thời gian quay vòng",
            'CarouselDirection': "走马灯方向 Hướng quay vòng",
            'Horizontal': "水平 Ngang",
            'Vertical': "垂直 Dọc"
        },
        bar: {
            "VerticalDisplay": "竖展示 Hiển thị dọc",
            "BarSettings": "柱体设置 Cấu hình cột",
            "MaxWidth": "最大宽度 Chiều rộng tối đa",
            "RoundCorner": "圆角 Góc tròn",
            "MinHeight": "最小高度 Chiều cao tối thiểu",
        },
        main: {
            "TitleSettings": "标题设置 Cấu hình tiêu đề",
            "Title": "标题 Tiêu đề",
            "TitleDisplay": "标题显示 Hiển thị tiêu đề",
            "FontColor": "字体颜色 Màu chữ",
            "FontSize": "字体大小 Kích thước chữ",
            "FontPosition": "字体位置 Vị trí chữ",
            "Subtitle": "副标题 Tiêu đề phụ",
            "X-axisSettings": "X轴设置 Cấu hình trục X",
            "Name": "名称 Tên",
            "Display": "显示 Hiển thị",
            "DisplayGridLine": "显示网格线 Hiển thị đường lưới",
            "LabelSpacing": "标签间距 Khoảng cách nhãn",
            "TextAngle": "文字角度 Góc chữ",
            "AxisInversion": "轴反转 Ngược trục",
            "AxisColor": "轴线颜色 Màu đường trục",
            "Y-axisSettings": "Y轴设置 Cấu hình trục Y",
            "AxisGridLines": "轴网格线 Đường lưới trục",
            "Inversion": "反转 Ngược",
            "ValueSettings": "数值设置 Cấu hình giá trị",
            "DisplayFormat": "显示格式 Định dạng hiển thị",
            "DisplayPosition": "显示位置 Vị trí hiển thị",
            "FontWeight": "字体粗细 C độ dày chữ",
            "PromptSettings": "提示语设置 Cấu hình lời nhắc",
            "BackgroundColor": "背景颜色 Màu nền",
            "AxisMarginSettings": "坐标轴边距设置 Cấu hình khoảng cách trục",
            "LeftMargin": "左边距(像素) Khoảng cách bên trái (pixel)",
            "TopMargin": "顶边距(像素) Khoảng cách trên (pixel)",
            "RightMargin": "右边距(像素) Khoảng cách bên phải (pixel)",
            "BottomMargin": "底边距(像素) Khoảng cách dưới (pixel)",
            "LegendOperation": "图例操作 Thao tác legend",
            "Legend": "图例 Legend",
            "IndependentColor": "独立颜色 Màu độc lập",
            "Position": "位置 Vị trí",
            "RelativePosition": "相对位置 Vị trí tương đối",
            "LayoutOrientation": "布局朝向 Hướng đăng ký",
            "CustomColor": "自定义配色 Màu tùy chỉnh",
        },
        borderBox: {
            'DecorationType': "装饰类型 Loại trang trí",
            'MainColor': "主颜色 Màu chính",
            'SecondaryColor': "副颜色 Màu phụ",
            'BackgroundColor': "背景色 Màu nền",
            'AnimationDuration': "动画时长 Thời lượng hoạt ảnh"
        },
        clappr: {},
        common: {

            'ComponentLibrary': "组件库 Thư viện thành phần",
            'AddToComponentLibrary': "加入组件库 Thêm vào thư viện thành phần",
            'DynamicParameter': "动态参数 Tham số động態",
            'NoData': "暂无 Không có",
        },
        datav: {},
        datetime: {
            'TimeFormat': "时间格式 Định dạng thời gian",
            'CustomFormat': "自定义格式 Định dạng tùy chỉnh",
            'FontSpacing': "字体间距 Khoảng cách chữ",
            'FontSize': "字体大小 Kích thước chữ",
            'FontBackground': "字体背景 Nền chữ",
            'Alignment': "对其方式 Cách căn",
            'FontWeight': "文字粗细 C độ dày chữ",
            'FontColor': "字体颜色 Màu chữ"
        },
        decoration: {
            'DecorationType': "装饰类型 Loại trang trí",
            'MainColor': "主颜色 Màu chính",
            'SecondaryColor': "副颜色 Màu phụ",
            'BackgroundColor': "背景色 Màu nền",
            'AnimationDuration': "动画时长 Thời lượng hoạt ảnh"
        },
        flop: {
            'Overall': "整体 Toàn體",
            'Accuracy': "精度 Độ chính xác",
            'LengthandWidth': "长宽 Dài và rộng",
            'Margin': "外边距 Khoảng cách bên ngoài",
            'Padding': "内边距 Khoảng cách bên trong",
            'Border': "边框 Viền",
            'BorderColor': "边框颜色 Màu viền",
            'BorderWidth': "边框宽度 Chiều rộng viền",
            'BackgroundColor': "背景颜色 Màu nền",
            'ImageAddress': "图片地址 Địa chỉ hình ảnh",
            'ContentSettings': "内容设置 Cấu hình Nội dung",
            'Content': "内容 Nội dung",
            'FontSize': "字体大小 Kích thước chữ",
            'FontColor': "字体颜色 Màu chữ",
            'FontWeight': "文字粗细 C độ dày chữ",
            'Alignment': "对其方式 Cách căn",
            'PrefixSettings': "前缀设置 Cấu hình tiền tố",
            'NoLineBreak': "不换行 Không換 dòng",
            'PrefixContent': "前缀内容 Nội dung tiền tố",
            'Color': "颜色 Màu sắc",
            'FontLineHeight': "字体行高 Chiều cao dòng chữ",
            'SuffixSettings': "后缀设置 Cấu hình hậu tố",
            'SuffixContent': "后缀内容 Nội dung hậu tố"
        },
        funnel: {
            'TextInside': "文字内部 Chữ bên trong",
        },
        gauge: {
            'ScaleValue': "刻度值 Giá trị thước",
            'ScaleFontSize': "刻度字号 Kích thước chữ thước",
            'ScaleThickness': "刻度粗度 Độ dày thước",
            'ScaleColor': "刻度颜色 Màu thước",
            'IndicatorFontSize': "指标字体大小 Kích thước chữ chỉ báo",
            'IndicatorUnit': "指标单位 Đơn vị chỉ báo"
        },
        iframe: {
            'Address': "地址 Địa chỉ",
        },
        img: {
            'Rotate': "开启旋转 Bật quay",
            'Opacity': "透明度 Mức trong suốt",
            'BorderRadius': "边框圆角 Góc bo viền",
            'Duration': "旋转时间 Thời gian quay",
            'PicAddress': "图片地址 Địa chỉ hình ảnh",
        },
        imgBorder: {
            'BackgroundColor': "背景色 Màu nền",
            'Opacity': "透明度 Mức trong suốt",
            'PicAddress': "图片地址 Địa chỉ hình ảnh",
        },
        line: {
            'LineChartSettings': "折线设置 Cấu hình biểu đồ đường",
            'SmoothCurve': "平滑曲线 Đường cong 부드러",
            'AreaStacking': "面积堆积 Tích lũy khu vực",
            'LineWidth': "线条宽度 Chiều rộng đường",
            'CircleDot': "圆点 Điểm tròn",
            'SizeoftheDot': "点的大小 Kích thước điểm"
        },
        map: {
            'MapSelection': "地图选择 Chọn bản đồ",
            'MapScale': "地图比例 Tỷ lệ bản đồ",
            'MapZoom': "地图缩放 Loup bản đồ",
            'CarouselSettings': "轮播设置 Cấu hình quay vòng",
            'TurnonCarousel': "开启轮播 Bật quay vòng",
            'CarouselTime': "轮播时间 Thời gian quay vòng",
            'BasicSettings': "基本设置 Cấu hình cơ bản",
            'FontSize': "字体大小 Kích thước chữ",
            'FontHighlightColor': "字体高亮颜色 Màu chữ nổi bật",
            'FontColor': "字体颜色 Màu chữ",
            'BorderColor': "边框颜色 Màu viền",
            'RegionSettings': "区域设置 Cấu hình khu vực",
            'RegionLine': "区域线 Đường khu vực",
            'RegionColor': "区域颜色 Màu khu vực",
            'RegionHighlightColor': "区域高亮颜色 Màu nổi bật khu vực",
            'TipSettings': "提示设置 Cấu hình lời nhắc",
            'BackgroundColor': "背景色 Màu nền",
            'TextColor': "文字颜色 Màu chữ",
            'TextSize': "文字大小 Kích thước chữ"
        },
        pictorialBar: {
            'TitleColor': "标题颜色 Màu tiêu đề",
            'TitleSize': "标题大小 Kích thước tiêu đề",
            'ValueColor': "数值颜色 Màu giá trị",
            'ValueSize': "数值大小 Kích thước giá trị",
            'Icon': "图标 Biểu tượng",
            'IconSize': "图标大小 Kích thước biểu tượng",
            'Spacing': "间距 Khoảng cách"
        },
        pie: {
            'PieChartSettings': "饼图设置 Cấu hình biểu đồ tròn",
            'SetAsRing': "设置为环形 Đặt làm vòng tròn",
            'NightingaleRose': "南丁格尔玫瑰 Hồng Nhạn Đình",
            'AutoSort': "自动排序 Sắp xếp tự động",
            'DoNotDisplayZero': "不展示零 Không hiển thị số 0"
        },
        radar: {
            'AreaOpacity': "区域透明度 Mức trong suốt khu vực",
        },
        rectangle: {},
        scatter: {},
        swiper: {
            'Type': "类型 Loại",
            'Interval': "轮播时间 Thời gian quay vòng",
            'Indicator': "选择器 Bộ chọn",
            'Opacity': "透明度 Mức trong suốt",
            'Autoplay': "自动播放 Chơi tự động",
            'Controls': "视频控制台 Bảng điều khiển video",
            'Loop': "循环播放 Chơi vòng lặp",
        },
        table: {
            'OpenRanking': "开启排名 Bật sắp xếp hạng",
            'RankingWidth': "排名宽度 Chiều rộng hạng",
            'Border': "边框 Viền",
            'RoundCorner': "圆角 Góc tròn",
            'AppendMode': "追加模式 Chế độ thêm",
            'EnableMergeColumn': "启用合并列 Bật gộp cột",
            'EnableTextScroll': "启用文本滚动 Bật cuộn văn bản",
            'EnableScrolling': "开启滚动 Bật cuộn",
            'ScrollInterval': "滚动间隔 Khoảng cách cuộn",
            'ScrollSpeed': "滚动速度 Tốc độ cuộn",
            'HeaderSettings': "表头设置 Cấu hình tiêu đề bảng",
            'Display': "显示 Hiển thị",
            'FontSize': "字体大小 Kích thước chữ",
            'ColumnHeaderHeight': "列头高度 Chiều cao tiêu đề cột",
            'BackgroundColor': "背景颜色 Màu nền",
            'FontColor': "字体颜色 Màu chữ",
            'AlignmentMethod': "对其方式 Cách căn",
            'TableSettings': "表格设置 Cấu hình bảng",
            'NumberofDisplayedRows': "显示行数 Số dòng hiển thị",
            'TextColor': "文字颜色 Màu chữ",
            'OddRowColor': "奇行颜色 Màu dòng lẻ",
            'EvenRowColor': "偶行颜色 Màu dòng chẵn",
            'TableColumnSettings': "表格列设置 Cấu hình cột bảng",
            'DefaultConfiguration': "默认配置 Cấu hình mặc định",
            'RowBackground': "行背景 Nền dòng",
            'PleaseSelectColor': "请选择颜色 Vui lòng chọn màu",
            'RowFont': "行字体 Chữ體 dòng",
            'CellBackground': "单元格背景 Nền ô",
            'CellFont': "单元格字体 Chữ體 ô",
            'Condition': "条件 Điều kiện",
            'Value': "值 Giá trị",
            'PleaseSelectCondition': "请选择条件 Vui lòng chọn điều kiện",
            'PleaseEnterValue': "请输入值 Vui lòng nhập giá trị",
            'Formatting': "格式化 Định dạng",
            'Edit': "编辑 Sửa",
            'FormattingTAB': "格式化TAB Định dạng TAB",
            'EditData': "编辑数据 Sửa dữ liệu",
            'LoadModel': "加载模型 Mô hình tải"
        },
        tabs: {
            'FontSize': "字体大小 Kích thước chữ",
            'FontColor': "字体颜色 Màu chữ",
            'Type': "类型 Loại",
            'FontSpacing': "字体间距 Khoảng cách chữ",
            'BorderSettings': "边框设置 Cấu hình viền",
            'BackgroundColor': "背景颜色 Màu nền",
            'IconSpacing': "图标间距 Khoảng cách biểu tượng",
            'IconSize': "图标大小 Kích thước biểu tượng",
            'ImageAddress': "图片地址 Địa chỉ hình ảnh",
            'BorderColor': "边框颜色 Màu viền",
            'BorderWidth': "边框宽度 Chiều rộng viền",
            'HighlightSettings': "高亮设置 Cấu hình nổi bật",
            'FontHighlightColor': "字体高亮颜色 Màu chữ nổi bật",
            'BackgroundImage': "背景图片 Hình nền"

        },
        text: {
            'TextContent': "文本内容 Nội dung văn bản",
            'FontType': "字体类型 Kiểu chữ",
            'FontSize': "字体大小 Kích thước chữ",
            "FontColor": "字体颜色 Màu chữ",
            'FontSpacing': "字体间距 Khoảng cách chữ",
            'FontLineHeight': "字体行高 Chiều cao dòng chữ",
            'FontBackground': "字体背景 Nền chữ",
            'FontWeight': "文字粗细 C độ dày chữ",
            'Alignment': "对齐方式 Cách căn",
            'MarqueeSettings': "跑马灯设置 Cấu hình跑马灯",
            'TurnOn': "开启 Bật",
            'ScrollingSpeed': "滚动速度 Tốc độ cuộn",
            'HyperlinkSettings': "超链接设置 Cấu hình siêu liên kết",
            'OpeningMethod': "打开方式 Cách mở",
            'HyperlinkAddress': "超链地址 Địa chỉ siêu liên kết"
        },
        time: {
            'ExecutionInterval': "执行间隔 Khoảng cách thực thi",
            'ExecutionLogic': "执行逻辑 Logic thực thi",
            'Edit': "编辑 Sửa",
        },
        video: {
            'Cover': "封面 Bìa",
            'Address': "地址 Địa chỉ",
            'Autoplay': "自动播放 Chơi tự động",
            'Controls': "控制台 Bảng điều khiển",
            'Loop': "循环播放 Chơi vòng lặp",
        },
        vue: {},
        wordCloud: {
            'MinimumFont': "最小字体 Chữ nhỏ nhất",
            'MaximumFont': "最大字体 Chữ lớn nhất",
            'Spacing': "间距 Khoảng cách",
            'Rotation': "旋转 Quay"
        },
        progress: {
            'Description': "描述 Mô tả",
            'Type': "类型 Loại",
            'Width': "宽度 Chiều rộng",
            'Color': "颜色 Màu sắc",
            'BackgroundColor': "背景颜色 Màu nền",
            'FontSize': "字体大小 Kích thước chữ",
            'FontColor': "字体颜色 Màu chữ",
            'FontWeight': "字体粗细 C độ dày chữ",
            'PrefixFontSize': "前缀字体大小 Kích thước chữ tiền tố",
            'PrefixFontColor': "前缀字体颜色 Màu chữ tiền tố",
            'PrefixFontWeight': "前缀文字粗细 C độ dày chữ tiền tố"
        }
    },
    '新建大屏': "新建大屏 Tạo màn hình lớn mới",
    '编辑大屏': "编辑大屏 Sửa màn hình lớn",
    '复制大屏': "复制大屏 Sao chép màn hình lớn",
    /////template page start///////
    "编辑模板": "编辑模板 Sửa mẫu",
    "新建模板": "新建模板 Tạo mẫu mới",
    "复制模板": "复制模板 Sao chép mẫu",
    /////template page end///////
    '柱状图': "柱状图 Biểu đồ cột",
    '图表': "图表 Biểu đồ",
    '文字': "文字 Văn bản",
    '媒体': "媒体 Media",
    '指标': "指标 Chỉ báo",
    '表格': "表格 Bảng",
    '边框': "边框 Viền",
    'datav例子': "datav例子 Ví dụ datav",
    '装饰': "装饰 Trang trí",
    '二次开发': "二次开发 Phát triển thứ hai",
    '通用型': "通用型 Loại chung",
    '柱形图': "柱形图 Biểu đồ cột",
    '折线图': "折线图 Biểu đồ đường",
    '饼图': "饼图 Biểu đồ tròn",
    '象形图': "象形图 Biểu đồ tượng hình",
    '雷达图': "雷达图 Biểu đồ radar",
    '散点图': "散点图 Biểu đồ điểm rải",
    '漏斗图': "漏斗图 Biểu đồ cốc cống",
    '地图': "地图 Bản đồ",
    '矩形图': "矩形图 Biểu đồ hình chữ nhật",
    '定时器': "定时器 Bộ định thời",
    '自定义Vue组件': "自定义Vue组件 Thành phần Vue tùy chỉnh",
    '文本框': "文本框 Khung văn bản",
    '跑马灯': "跑马灯 Đèn跑马",
    '超链接': "超链接 Siêu liên kết",
    '实时时间': "实时时间 Thời gian thực hiện",
    '图片': "图片 Hình ảnh",
    '图片框': "图片框 Khung hình ảnh",
    '轮播图': "轮播图 Hình ảnh quay vòng",
    'video播放器': "video播放器 Máy phát video",
    'hls播放器': "hls播放器 Máy phát hls",
    '翻牌器': "翻牌器 Máy lật thẻ",
    '颜色块': "颜色块 Khối màu",
    '环形图': "环形图 Biểu đồ vòng tròn",
    '进度条': "进度条 Thanh tiến độ",
    '仪表盘': "仪表盘 Bảng điều khiển",
    '字符云': "字符云 Mây ký tự",
    '表格': "表格 Bảng",
    '边框1': "边框1 Viền 1",
    '边框12': "边框12 Viền 12",
    '滚动排名': "滚动排名 Sắp xếp hạng cuộn",
    '胶囊排名': "胶囊排名 Sắp xếp hạng viên nang",
    '水位图': "水位图 Biểu đồ nước",
    '进度池': "进度池 Bể tiến độ",
    '锥形柱图': "锥形柱图 Biểu đồ cột hình nón",
    '动态环图': "动态环图 Biểu đồ vòng động态",
    '装饰1': "装饰1 Trang trí 1",
    '装饰12': "装饰12 Trang trí 12",
    '自定义组件': "自定义组件 Thành phần tùy chỉnh",
    '滚动选项卡': "滚动选项卡 Thẻ cuộn",
    '滚动列表': "滚动列表 Danh sách cuộn",
    '选项卡': "选项卡 Thẻ",
    '飞线图': "飞线图 Biểu đồ đường bay",
    //////表格的列配置//////////
    '配置项': "配置项 Thiết lập cấu hình",
    '名称': "名称 Tên",
    'key值': "key值 Giá trị khóa",
    '宽度': "宽度 Chiều rộng",
    '状态': "状态 Trạng thái",
    '换行': "换行换 dòng"
}
# 设备监控台光点流动效果

## 功能说明

设备监控台控件现在支持在连接线上显示流动的光点效果，用于模拟数据流、能量流或信号传输的视觉效果。

## 效果特点

- ✨ **流动光点**: 沿着连接线流动的发光圆点
- 🎨 **可自定义颜色**: 支持自定义光点颜色
- ⚡ **高性能**: 使用SVG动画，性能优异
- 🔧 **可控制**: 支持开启/关闭光点效果
- 📱 **兼容性好**: 支持所有现代浏览器

## 配置选项

### 基础配置

```javascript
const option = {
  // 其他配置...
  
  // 光点效果配置
  flowDotsEnabled: true,        // 是否启用光点效果，默认true
  flowDotColor: '#00ff88',      // 光点颜色，默认绿色
  connectionLineColor: '#537895' // 连接线颜色
};
```

### 配置说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `flowDotsEnabled` | Boolean | `true` | 是否启用光点流动效果 |
| `flowDotColor` | String | `'#00ff88'` | 光点颜色，支持任何CSS颜色值 |
| `connectionLineColor` | String | `'#537895'` | 连接线颜色 |

## 使用示例

### 基础使用

```html
<!DOCTYPE html>
<html>
<head>
    <title>设备监控台光点效果</title>
</head>
<body>
    <div id="chart" style="width: 800px; height: 600px;"></div>
    
    <script src="../dist/index.js"></script>
    <script>
        const option = {
            backgroundColor: '#0f1419',
            connectionLineColor: '#537895',
            flowDotsEnabled: true,
            flowDotColor: '#00ff88',
            devices: [
                { id: 'server1', name: '服务器1', x: 100, y: 100, status: 'running' },
                { id: 'switch1', name: '交换机1', x: 300, y: 100, status: 'running' }
            ],
            connections: [
                { from: 'server1', to: 'switch1' }
            ]
        };
        
        const chart = new CI.EquipmentMonitoring(document.getElementById('chart'), option);
    </script>
</body>
</html>
```

### 动态控制光点效果

```javascript
// 创建图表实例
const chart = new CI.EquipmentMonitoring(document.getElementById('chart'), option);

// 开启光点效果
function enableFlowDots() {
    const currentOption = chart.getOption();
    currentOption.flowDotsEnabled = true;
    chart.setOption(currentOption);
}

// 关闭光点效果
function disableFlowDots() {
    const currentOption = chart.getOption();
    currentOption.flowDotsEnabled = false;
    chart.setOption(currentOption);
}

// 更改光点颜色
function changeFlowDotColor(color) {
    const currentOption = chart.getOption();
    currentOption.flowDotColor = color;
    chart.setOption(currentOption);
}
```

### 不同颜色主题示例

```javascript
// 蓝色主题
const blueTheme = {
    backgroundColor: '#0a0e27',
    connectionLineColor: '#1e3a8a',
    flowDotColor: '#3b82f6',
    flowDotsEnabled: true
};

// 红色主题
const redTheme = {
    backgroundColor: '#1a0a0a',
    connectionLineColor: '#7f1d1d',
    flowDotColor: '#ef4444',
    flowDotsEnabled: true
};

// 绿色主题
const greenTheme = {
    backgroundColor: '#0a1a0a',
    connectionLineColor: '#166534',
    flowDotColor: '#22c55e',
    flowDotsEnabled: true
};
```

## 技术实现

### SVG动画

光点效果使用SVG的 `animateMotion` 和 `animate` 元素实现：

- `animateMotion`: 控制光点沿路径移动
- `animate`: 控制光点的透明度变化
- 每条连接线创建3个光点，错开时间形成连续流动效果

### 性能优化

- 使用原生SVG动画，无需JavaScript计算
- 光点数量控制在合理范围内
- 支持动态开启/关闭以节省资源

## 测试页面

运行 `demo/equipment-monitoring-flow-dots-test.html` 查看完整的测试示例，包含：

- 简单连接测试
- 复杂网络测试  
- 星型拓扑测试
- 动态开关控制

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

## 注意事项

1. 光点效果在设备数量较多时可能影响性能，建议在必要时关闭
2. 光点颜色建议与整体主题保持协调
3. 在移动设备上建议适当减少光点数量或关闭效果

## 常见问题

**Q: 光点不显示怎么办？**
A: 检查 `flowDotsEnabled` 是否为 `true`，确保浏览器支持SVG动画。

**Q: 如何调整光点速度？**
A: 目前光点速度固定为3秒一个周期，如需调整请修改组件源码中的 `dur` 属性。

**Q: 可以自定义光点大小吗？**
A: 目前光点大小固定为半径4px，如需调整请修改组件源码中的 `r` 属性。

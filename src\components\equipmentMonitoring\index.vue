<template>
  <div class="equipment-monitoring-container" :style="containerStyle">

    <header v-if="option.showHeader" class="equipment-header">
      <h1 :style="headerStyle">{{ option.title || '产线设备实时监控' }}</h1>
    </header>
    <main class="device-layout" ref="deviceLayout" :style="layoutStyle">
      <div ref="zoomContainer" class="zoom-container">
        <!-- 设备将由 JavaScript 动态生成 -->
      </div>
    </main>

    <!-- 设备详情弹框 - 使用Element UI Dialog -->
    <!-- <vxe-modal v-model="showModal"  :title="modalTitle" :width="600" :height="400">
      <el-tabs v-model="activeTab" type="border-card">
          <el-tab-pane
            v-for="tab in tabs"
            :key="tab.key"
            :label="tab.label"
            :name="tab.key"
          >
            <div v-if="tab.key === 'params'" class="tab-content">
              <el-row :gutter="20" v-for="param in currentDeviceParams" :key="param.key">
                <el-col :span="8">
                  <strong>{{ param.label }}:</strong>
                </el-col>
                <el-col :span="16">
                  <span class="param-value">{{ param.value }}</span> {{ param.unit }}
                </el-col>
              </el-row>
            </div>
            <div v-if="tab.key === 'maintenance'" class="tab-content">
              <el-row :gutter="20">
                <el-col :span="8">
                  <strong>上次保养时间:</strong>
                </el-col>
                <el-col :span="16">
                  <span class="param-value">{{ currentDeviceMaintenance.date }}</span>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <strong>保养负责人:</strong>
                </el-col>
                <el-col :span="16">
                  <span class="param-value">{{ currentDeviceMaintenance.by }}</span>
                </el-col>
              </el-row>
            </div>
            <div v-if="tab.key === 'alarm'" class="tab-content">
              <el-row :gutter="20">
                <el-col :span="8">
                  <strong>报警代码:</strong>
                </el-col>
                <el-col :span="16">
                  <span class="param-value alarm-code">{{ currentDeviceAlarm.code }}</span>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <strong>报警信息:</strong>
                </el-col>
                <el-col :span="16">
                  <span class="param-value alarm-message">{{ currentDeviceAlarm.message }}</span>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <strong>发生时间:</strong>
                </el-col>
                <el-col :span="16">
                  <span class="param-value">{{ currentDeviceAlarm.time }}</span>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
        </el-tabs>
    </vxe-modal> -->
    <!-- 摄像头视频弹框 - 使用Element UI Dialog -->
    <vxe-modal v-model="showCameraModal" showFooter :title="cameraTitle" :width="900" :height="600">
        <div class="camera-container">
            <!-- HLS播放器容器 -->
            <div v-if="currentCameraUrl && isHlsUrl(currentCameraUrl)"
                :id="hlsPlayerId"
                class="hls-player-container">
            </div>
            <!-- 普通视频播放器 -->
            <video v-else-if="currentCameraUrl"
                  :src="currentCameraUrl"
                  controls
                  autoplay
                  muted
                  class="video-player"
                  style="width: 100%; height: 450px; object-fit: cover;">
            </video>
            <!-- 占位图片 -->
            <div v-else class="camera-placeholder">
              <div class="placeholder-icon">
                <!-- 摄像头SVG图标 -->
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"
                    style="width: 64px; height: 64px; color: #909399;">
                  <path d="M336 112c-8.8 0-16 7.2-16 16v48H192V128c0-8.8-7.2-16-16-16s-16 7.2-16 16v48H80c-8.8 0-16 7.2-16 16v192c0 8.8 7.2 16 16 16h96v48c0 8.8 7.2 16 16 16s16-7.2 16-16v-48h128v48c0 8.8 7.2 16 16 16s16-7.2 16-16v-48h96c8.8 0 16-7.2 16-16V176c0-8.8-7.2-16-16-16h-96V128c0-8.8-7.2-16-16-16zm-16 144H192v-64h128v64z"/>
                </svg>
              </div>
              <p>暂无摄像头数据</p>
            </div>
          </div>
          <div slot="footer" class="dialog-footer">
            <el-button @click="closeCameraModal">关闭</el-button>
            <el-button v-if="currentCameraUrl" type="primary" @click="refreshCamera">刷新</el-button>
          </div>
    </vxe-modal>
    <!-- 工具提示 -->
    <div 
      v-if="showTooltip" 
      class="device-tooltip" 
      :style="tooltipStyle"
    >
      <div v-for="(value, key) in tooltipData" :key="key">
        <strong>{{ key }}:</strong> {{ value }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'equipmentMonitoringType', // 这个名称可以随意，不能和src\echart\packages下的名称相同
  props: {
    option: {
      type: Object,
      default: () => ({})
    },
    // 数据源相关props（从父组件传入）
    dataType: {
      type: Number,
      default: 0
    },
    dataMethod: {
      type: String,
      default: "get"
    },
    url: {
      type: String
    },
    styleSizeName: String,
    dataQuery: String,
    dataHeader: String,
    dataFormatter: Function,
    dataSet: String,
    time: {
      type: Number,
      default: 0
    },
    data: {
      type: [Object, String, Array]
    }
  },
  data() {
    return {
      devices: [],
      showModal: false,
      showCameraModal: false,
      showTooltip: false,
      modalTitle: '',
      cameraTitle: '',
      activeTab: 'params',
      currentDevice: null,
      currentCameraUrl: '',
      hlsPlayer: null,
      hlsPlayerId: '',
      tooltipData: {},
      tooltipPosition: { x: 0, y: 0 },
      pollingTimer: null,
      currentScale: 1,
      currentOffset: { x: 0, y: 0 },
      tabs: [
        { key: 'params', label: '关键参数' },
        { key: 'maintenance', label: '保养信息' },
        { key: 'alarm', label: '报警详情' }
      ]
    }
  },
  computed: {
    containerStyle() {
      const bgColor = this.option.backgroundColor || '#1a1a2e'
      const opacity = this.option.backgroundOpacity !== undefined ? this.option.backgroundOpacity : 1

      return {
        width: '100%',
        height: '100%',
        backgroundColor: opacity < 1 ? 'transparent' : bgColor,
        background: opacity < 1 ? `rgba(${this.hexToRgb(bgColor)}, ${opacity})` : bgColor,
        color: this.option.textColor || '#dcdcdc',
        fontFamily: this.option.fontFamily || 'Noto Sans SC, sans-serif'
      }
    },
    headerStyle() {
      return {
        fontSize: this.option.headerFontSize || '2.5rem',
        color: this.option.headerColor || '#e94560',
        textAlign: this.option.headerAlign || 'center',
        textShadow: `0 0 10px ${this.option.headerColor || '#e94560'}`,
        marginBottom: '20px'
      }
    },
    tooltipStyle() {
      return {
        position: 'absolute',
        left: this.tooltipPosition.x + 'px',
        top: this.tooltipPosition.y+100 + 'px',
        backgroundColor: 'rgba(0, 0, 0, 0.9)',
        color: 'white',
        padding: '10px 14px',
        borderRadius: '6px',
        pointerEvents: 'none',
        zIndex: 1000,
        fontSize: '13px',
        lineHeight: '1.4',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
        border: '1px solid rgba(255, 255, 255, 0.1)',
        maxWidth: '200px',
        whiteSpace: 'nowrap'
      }
    },
    layoutStyle() {
      const bgColor = this.option.layoutBackgroundColor || '#16213e'
      const opacity = this.option.backgroundOpacity !== undefined ? this.option.backgroundOpacity : 1

      return {
        position: 'relative',
        width: '100%',
        height: this.option.showHeader ? 'calc(100% - 80px)' : '100%',
        overflow: 'hidden',
        //background:'transparent'
        backgroundColor: opacity < 1 ? 'transparent' : bgColor,
        background: opacity < 1 ? `rgba(${this.hexToRgb(bgColor)}, ${opacity})` : bgColor,
        // border: '1px solid #0f3460',
        // borderRadius: '10px',
        // backgroundImage: 'radial-gradient(circle, #0f3460 1px, transparent 1px)',
        backgroundSize: '20px 20px'
      }
    },
    currentDeviceParams() {
      if (!this.currentDevice) return []
      return [
        { key: 'temp', label: '温度', value: (Math.random() * 20 + 60).toFixed(1), unit: '°C' },
        { key: 'pressure', label: '压力', value: (Math.random() * 50 + 100).toFixed(1), unit: 'kPa' },
        { key: 'speed', label: '运行速度', value: (Math.random() * 10 + 5).toFixed(1), unit: 'm/min' }
      ]
    },
    currentDeviceMaintenance() {
      return {
        date: '2024-01-15',
        by: '张工程师'
      }
    },
    currentDeviceAlarm() {
      return {
        code: 'E001',
        message: '温度超限',
        time: '2024-01-17 14:30:25'
      }
    }
  },
  mounted() {
    this.initializeLayout()
    this.startPolling()
    this.hlsPlayerId = 'hls-player-' + Date.now()
  },
  beforeDestroy() {
    if (this.pollingTimer) {
      clearInterval(this.pollingTimer)
    }
    window.removeEventListener('resize', this.updateZoom)

    // 清理流动点
    this.cleanupFlowDots()

    // 清理HLS播放器
    this.destroyHlsPlayer()
  },
  methods: {
    // 辅助方法：将十六进制颜色转换为RGB
    hexToRgb(hex) {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
      return result ?
        `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}` :
        '26, 26, 46' // 默认值
    },
    async initializeLayout() {
      try {
        const deviceData = await this.getDeviceData()
        this.devices = deviceData
        this.$nextTick(() => {
          this.renderDevices()
          this.updateZoom()
          window.addEventListener('resize', this.updateZoom)
        })
      } catch (error) {
        console.error('Failed to load device data:', error)
      }
    },
    async getDeviceData() {
      //debugger
     
      // 优先使用从父组件传入的数据（动态数据源）
      if (this.data && Array.isArray(this.data) && this.data.length > 0) {
         console.log("getDeviceData:",this.data)
        return this.data
      }

      // 如果有自定义数据源，使用自定义数据
      if (this.option.customDevices && this.option.customDevices.length > 0) {
         console.log("自定义数据源.customDevices:",this.option.customDevices)
        return this.option.customDevices
      }

      // 否则使用默认的模拟数据
      console.log("====使用默认的模拟数据====")
      return this.getMockDeviceData()
    },
    getMockDeviceData() {
      const mockData = [
       { id: 'dev-01', name: '放板机', x: 50, y: 50, status: 'running', connections: ['dev-02'], tooltipData: { '型号': 'LDR-2000', '厂商': 'A-Tech', '投入日期': '2022-08-15' } },
    { id: 'dev-02', name: '激光打码', x: 200, y: 50, status: 'running', connections: ['dev-03'], icons: { camera: true }, cameraUrl: 'https://open.ys7.com/v3/openlive/E10668433_1_1.m3u8?expire=1664027961&id=363444666731335680&t=346b970f0d5cb5d5b5e4734d966275a61fb9efd5038c7a75b7435423c5716908&ev=100', tooltipData: { '型号': 'LM-500', '功率': '50W', '厂商': 'B-Laser' } },
    { id: 'dev-03', name: '线路前处理', x: 350, y: 50, status: 'running', connections: ['dev-04'], tooltipData: { '处理方式': '等离子清洗', '厂商': 'C-Process' } },
    { id: 'dev-04', name: '暂存机', x: 500, y: 50, status: 'idle', connections: ['dev-05'] },
    { id: 'dev-05', name: '整平机', x: 650, y: 50, status: 'running', connections: ['dev-06'] },
    { id: 'dev-06', name: '涂布机', x: 800, y: 50, status: 'running', connections: ['dev-07'] },
    { id: 'dev-07', name: '隧道炉', x: 950, y: 50, status: 'running', connections: ['dev-08'] },
    { id: 'dev-08', name: '整平机', x: 1100, y: 50, status: 'running', connections: ['dev-09'] },
    { id: 'dev-09', name: '暂存机', x: 1250, y: 50, status: 'idle', connections: ['dev-10'] },
    { id: 'dev-10', name: '线路DI', x: 1400, y: 50, status: 'running', connections: ['dev-11'], icons: { camera: true }, cameraUrl: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4' },
    { id: 'dev-11', name: 'NG暂存机', x: 1550, y: 50, status: 'idle', connections: ['dev-12'] },
    { id: 'dev-12', name: '显影蚀刻退膜', x: 1700, y: 50, status: 'running', connections: ['dev-13'], icons: { star: true } },
    { id: 'dev-13', name: 'AOI', x: 1850, y: 50, status: 'running', connections: ['dev-14', 'dev-13-alt'] },

    // Right Column, Main Flow (Down)
    { id: 'dev-14', name: '转角机1', x: 1850, y: 180, status: 'running', connections: ['dev-15', 'dev-18'] },
    { id: 'dev-15', name: '侧边一体机', x: 1850, y: 310, status: 'running', connections: ['dev-16'] },
    { id: 'dev-16', name: '打靶', x: 1850, y: 440, status: 'running', connections: ['dev-17'] },
    { id: 'dev-17', name: '转角机3', x: 1850, y: 570, status: 'running', connections: ['dev-22'] },

    // Right Column, Parallel Flow (Down)
    { id: 'dev-18', name: '转角机2', x: 2000, y: 180, status: 'running', connections: ['dev-19'] },
    { id: 'dev-19', name: '侧边一体机', x: 2000, y: 310, status: 'running', connections: ['dev-20'] },
    { id: 'dev-20', name: '打靶', x: 2000, y: 440, status: 'running', connections: ['dev-21'] },
    { id: 'dev-21', name: '转角机4', x: 2000, y: 570, status: 'idle', connections: ['dev-22'] },

    // AOI检修 Fork
    // { id: 'dev-13-alt', name: 'AOI检修', x: 1700, y: 180, status: 'idle', connections: ['dev-14'] },
    { id: 'dev-14-alt', name: 'AOI检修', x: 1700, y: 310, status: 'idle', connections: ['dev-19'] },
    // Bottom Row <-
    { id: 'dev-22', name: '阻焊前处理', x: 1700, y: 570, status: 'running', connections: ['dev-23'] },
    { id: 'dev-23', name: '暂存机', x: 1550, y: 570, status: 'idle', connections: ['dev-24'] },
    { id: 'dev-24', name: '阻焊涂布', x: 1400, y: 570, status: 'running', connections: ['dev-25'] },
    { id: 'dev-25', name: '隧道炉', x: 1250, y: 570, status: 'running', connections: ['dev-26'] },
    { id: 'dev-26', name: '先进先出暂存机', x: 1100, y: 570, status: 'idle', connections: ['dev-27'] },
    { id: 'dev-27', name: '暂存机', x: 950, y: 570, status: 'running', connections: ['dev-28'], icons: { camera: true }, cameraUrl: 'https://open.ys7.com/v3/openlive/E10668433_1_1.m3u8?expire=1664027961&id=363444666731335680&t=346b970f0d5cb5d5b5e4734d966275a61fb9efd5038c7a75b7435423c5716908&ev=100' },
    { id: 'dev-28', name: '阻焊DI', x: 800, y: 570, status: 'idle', connections: ['dev-29'] },
    { id: 'dev-29', name: 'NG暂存机', x: 650, y: 570, status: 'running', connections: ['dev-30'], icons: { star: true } },
    { id: 'dev-30', name: '显影', x: 500, y: 570, status: 'idle', connections: ['dev-31'] },
    { id: 'dev-31', name: '暂存机', x: 500, y: 440, status: 'idle', connections: ['dev-32'] },
    { id: 'dev-32', name: '文字', x: 350, y: 440, status: 'running', connections: ['dev-33'] },
    { id: 'dev-33', name: '隧道炉', x: 200, y: 440, status: 'running', connections: ['dev-34'] },
    { id: 'dev-34', name: '收板机', x: 50, y: 440, status: 'running', connections: [] }
      ]
      
      // 随机化状态
      mockData.forEach(device => {
        const rand = Math.random()
        if (rand < 0.15) { device.status = 'alarm' }
        else if (rand < 0.4) { device.status = 'idle' }
        else { device.status = 'running' }
      })
      
      return Promise.resolve(mockData)
    },
    renderDevices() {
      const zoomContainer = this.$refs.zoomContainer
      if (!zoomContainer) return

      // 清理流动点
      this.cleanupFlowDots()

      // 清空容器
      zoomContainer.innerHTML = ''

      // 渲染设备
      this.devices.forEach(device => {
        const devElement = document.createElement('div')
        devElement.className = `device ${device.status}`
        devElement.id = device.id
        devElement.style.left = `${device.x}px`
        devElement.style.top = `${device.y}px`
        devElement.style.position = 'absolute'
        devElement.style.width = '120px'
        devElement.style.height = '70px'
        devElement.style.border = '2px solid'
        devElement.style.borderRadius = '8px'
        devElement.style.display = 'flex'
        devElement.style.flexDirection = 'column'
        devElement.style.justifyContent = 'center'
        devElement.style.alignItems = 'center'
        devElement.style.cursor = 'pointer'
        devElement.style.fontSize = this.option.textSize+'px' || '20px'
        devElement.style.fontWeight = 'bold'
        devElement.style.textAlign = 'center'
        devElement.style.transition = 'all 0.3s ease'

        // 设置基础样式（参考原插件样式）
        devElement.style.backgroundColor = this.option.deviceBackgroundColor || '#0f3460'
        devElement.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.4)'

        // 根据状态设置边框颜色
        if (device.status === 'running') {
          devElement.style.borderColor = '#2ecc71'
          devElement.style.color = this.option.textColor || '#dcdcdc'
        } else if (device.status === 'idle') {
          devElement.style.borderColor = '#3498db'
          devElement.style.color = this.option.textColor || '#dcdcdc'
        } else if (device.status === 'alarm') {
          devElement.style.borderColor = '#e74c3c'
          devElement.style.color = this.option.textColor || '#dcdcdc'
          devElement.style.animation = 'pulse-alarm 1s infinite'
        }

        const name = document.createElement('div')
        name.className = 'device-name'
        name.textContent = device.name
        name.style.marginBottom = '5px'

        const icons = document.createElement('div')
        icons.className = 'device-icons'
        icons.style.display = 'flex'
        icons.style.gap = '5px'


      // 使用SVG图标替代Font Awesome
        let iconHtml = `
          <svg t="1752810120575"  style="display: none;" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2556" width="0.8rem" height="0.8rem"><path d="M534.8864 68.7104a51.2 51.2 0 0 1 22.8864 22.8864l429.1584 858.3168A51.2 51.2 0 0 1 941.1584 1024H82.8416a51.2 51.2 0 0 1-45.7728-74.0864L466.2272 91.5968a51.2 51.2 0 0 1 68.6592-22.8864z m-23.296 662.6304a42.1376 42.1376 0 0 0-31.3344 12.288 39.2192 39.2192 0 0 0-13.5168 31.3344 40.96 40.96 0 0 0 13.5168 31.3344 42.6496 42.6496 0 0 0 31.3344 12.9024c12.288 0 23.3472-4.3008 32.5632-12.288a43.0592 43.0592 0 0 0 12.9024-31.9488 40.7552 40.7552 0 0 0-12.9024-31.3344c-8.6016-8.6016-19.6608-12.288-32.5632-12.288z m38.7072-350.8224h-76.8l14.1312 314.5728h48.5376l14.1312-314.5728z" fill="#F56A6A" p-id="2557">
            </path>
            </svg>
        `

        if (device.icons?.camera) {
          iconHtml += `
           <svg t="1752809810073" class="camera-btn icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1536" width="1rem" height="1rem"><path d="M868.032 287.808a64 64 0 0 1 101.056 51.648l2.624 302.592a64 64 0 0 1-102.752 51.456l-206.912-157.536a64 64 0 0 1 1.728-103.104l204.256-145.056z" fill="#1296db" p-id="1537"></path><path d="M144 192h456.32a96 96 0 0 1 96 96v417.376a96 96 0 0 1-96 96H144a96 96 0 0 1-96-96V288a96 96 0 0 1 96-96z" fill="#1296db" p-id="1538"></path></svg>
          `
        }

        if (device.icons?.star) {
          iconHtml += `
           <svg t="1752810245408" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3554" width="1rem" height="1rem"><path d="M313.991837 914.285714c-20.37551 0-40.228571-6.269388-56.946939-18.808163-30.302041-21.942857-44.930612-58.514286-38.661225-95.085714l24.032654-141.061225c3.134694-18.285714-3.134694-36.571429-16.195919-49.110204L123.297959 509.910204c-26.644898-26.122449-36.04898-64.261224-24.555102-99.787755 11.493878-35.526531 41.795918-61.126531 78.889796-66.35102l141.583674-20.375511c18.285714-2.612245 33.959184-14.106122 41.795918-30.30204l63.216326-128.522449C440.946939 130.612245 474.383673 109.714286 512 109.714286s71.053061 20.897959 87.24898 54.334694L662.987755 292.571429c8.359184 16.195918 24.032653 27.689796 41.795918 30.30204l141.583674 20.375511c37.093878 5.22449 67.395918 30.82449 78.889796 66.35102 11.493878 35.526531 2.089796 73.665306-24.555102 99.787755l-102.4 99.787755c-13.061224 12.538776-19.330612 31.346939-16.195919 49.110204l24.032654 141.061225c6.269388 37.093878-8.359184 73.142857-38.661225 95.085714-30.302041 21.942857-69.485714 24.555102-102.4 7.314286L538.122449 836.440816c-16.195918-8.359184-35.526531-8.359184-51.722449 0l-126.955102 66.87347c-14.628571 7.314286-30.302041 10.971429-45.453061 10.971428z m162.481632-96.653061z" fill="#F2CB51" p-id="3555"></path></svg>
          `
        }

        icons.innerHTML = iconHtml
        icons.style.position = 'absolute'
        icons.style.top = '5px'
        icons.style.right = '5px'
        icons.style.display = 'flex'
        icons.style.gap = '5px'

        devElement.appendChild(name)
        devElement.appendChild(icons)

        // 添加事件监听器
        if (device.tooltipData) {
          devElement.addEventListener('mouseover', (event) => {
            // 如果鼠标悬停在摄像头图标上，不显示tooltip
            if (!event.target.closest('.camera-btn')) {
              this.showTooltip = true
              this.tooltipData = device.tooltipData
              this.updateTooltipPosition(event)
            }
          })

          devElement.addEventListener('mouseout', (event) => {
            // 确保鼠标完全离开设备元素才隐藏tooltip
            if (!devElement.contains(event.relatedTarget)) {
              this.showTooltip = false
            }
          })

          devElement.addEventListener('mousemove', (event) => {
            // 如果鼠标在摄像头图标上，隐藏tooltip
            if (event.target.closest('.camera-btn')) {
              this.showTooltip = false
            } else if (this.showTooltip) {
              this.updateTooltipPosition(event)
            }
          })
        }

        // 点击事件处理
        devElement.addEventListener('click', (event) => {
          event.stopPropagation()

          // 检查是否点击了摄像头图标
          const cameraBtn = event.target.closest('.camera-btn')
          if (cameraBtn) {
            // 隐藏tooltip
            this.showTooltip = false
            this.openCameraModal(device)
          } else {
            this.openDeviceModal(device)
          }
        })

        // 为摄像头图标添加特殊的鼠标事件
        const cameraIcon = icons.querySelector('.camera-btn')
        if (cameraIcon) {
          cameraIcon.addEventListener('mouseenter', () => {
            this.showTooltip = false
            cameraIcon.style.opacity = '1'
            cameraIcon.style.transform = 'scale(1.2)'
          })

          cameraIcon.addEventListener('mouseleave', () => {
            cameraIcon.style.opacity = '0.7'
            cameraIcon.style.transform = 'scale(1)'
          })
        }

        zoomContainer.appendChild(devElement)
      })

      // 绘制连接线（确保DOM更新后执行）
      this.$nextTick(() => {
        this.drawConnections()
      })
    },
    drawConnections() {
      const zoomContainer = this.$refs.zoomContainer
      if (!zoomContainer) return

      const svgNS = "http://www.w3.org/2000/svg"
      const svg = document.createElementNS(svgNS, 'svg')
      svg.setAttribute('width', '100%')
      svg.setAttribute('height', '100%')
      svg.style.position = 'absolute'
      svg.style.top = '0'
      svg.style.left = '0'
      svg.style.zIndex = '-1'

      // 处理连接线 - 支持两种格式
      const connections = []

      // 格式1: 独立的connections数组
      if (this.option.connections && Array.isArray(this.option.connections)) {
        this.option.connections.forEach(conn => {
          connections.push({ from: conn.from, to: conn.to })
        })
      }

      // 格式2: 设备内的connections属性
      this.devices.forEach(startDevice => {
        if (startDevice.connections) {
          startDevice.connections.forEach(endDeviceId => {
            connections.push({ from: startDevice.id, to: endDeviceId })
          })
        }
      })

      // 绘制所有连接线
      connections.forEach(conn => {
        const startDevice = this.devices.find(d => d.id === conn.from)
        const endDevice = this.devices.find(d => d.id === conn.to)
        if (!startDevice || !endDevice) return

        const startEl = document.getElementById(startDevice.id)
        const endEl = document.getElementById(endDevice.id)
        if (!startEl || !endEl) return

        const line = document.createElementNS(svgNS, 'path')
        const pathId = `path_${startDevice.id}_${endDevice.id}`
        line.setAttribute('id', pathId)

        const startX = startEl.offsetLeft + startEl.offsetWidth / 2
        const startY = startEl.offsetTop + startEl.offsetHeight / 2
        const endX = endEl.offsetLeft + endEl.offsetWidth / 2
        const endY = endEl.offsetTop + endEl.offsetHeight / 2

        let d = ''
        const isHorizontal = Math.abs(startY - endY) < 5
        const isVertical = Math.abs(startX - endX) < 5

        if (isHorizontal || isVertical) {
          d = `M ${startX} ${startY} L ${endX} ${endY}`
        } else {
          d = `M ${startX} ${startY} L ${startX} ${endY} L ${endX} ${endY}`
        }

        line.setAttribute('d', d)
        line.setAttribute('stroke', this.option.connectionLineColor || '#537895')
        line.setAttribute('stroke-width', '2')
        line.setAttribute('fill', 'none')
        svg.appendChild(line)

        // 添加流动点（使用HTML+CSS实现更好的兼容性）
        if (this.option.flowDotsEnabled !== false) {
          console.log("准备创建流动点，从", startDevice.id, "到", endDevice.id)
          this.createFlowingDotsHTML(startDevice, endDevice)
        }
      })

      zoomContainer.appendChild(svg)
    },
    createFlowingDotsHTML(startDevice, endDevice) {
      const zoomContainer = this.$refs.zoomContainer
      if (!zoomContainer) return

      const startEl = document.getElementById(startDevice.id)
      const endEl = document.getElementById(endDevice.id)
      if (!startEl || !endEl) return

      const dotColor = this.option.flowDotColor || '#00ff88'
      const flowDotSpeed = this.option.flowDotSpeed || 3000
      const flowDotsNumber = this.option.flowDotsNumber || 3

      // 计算起点和终点坐标
      const startX = startEl.offsetLeft + startEl.offsetWidth / 2
      const startY = startEl.offsetTop + startEl.offsetHeight / 2
      const endX = endEl.offsetLeft + endEl.offsetWidth / 2
      const endY = endEl.offsetTop + endEl.offsetHeight / 2

      console.log("创建HTML流动点，从", startX, startY, "到", endX, endY)

      // 创建多个流动点
      for (let i = 0; i < flowDotsNumber; i++) {
        const dot = document.createElement('div')
        dot.className = 'flow-dot-html'
        dot.style.position = 'absolute'
        dot.style.width = '10px'
        dot.style.height = '10px'
        dot.style.backgroundColor = dotColor
        dot.style.borderRadius = '50%'
        dot.style.boxShadow = `0 0 10px ${dotColor}`
        dot.style.left = `${startX - 4}px`
        dot.style.top = `${startY - 4}px`
        dot.style.zIndex = '10'
        dot.style.pointerEvents = 'none'

        // 设置动画
        const duration = flowDotSpeed // 3秒
        const delay = i * 1000 // 每个点延迟1秒

        setTimeout(() => {
          this.animateDot(dot, startX, startY, endX, endY, duration)
        }, delay)

        zoomContainer.appendChild(dot)
      }
    },
    animateDot(dot, startX, startY, endX, endY, duration) {
      if (!dot || !dot.parentNode) {
        console.log("流动点元素已被移除，停止动画")
        return
      }

      const startTime = Date.now()
      console.log("开始动画，从", startX, startY, "到", endX, endY)

      const animate = () => {
        if (!dot || !dot.parentNode) {
          console.log("动画过程中元素被移除，停止动画")
          return
        }

        const elapsed = Date.now() - startTime
        const progress = Math.min(elapsed / duration, 1)

        if (progress < 1) {
          // 计算当前位置
          const currentX = startX + (endX - startX) * progress
          const currentY = startY + (endY - startY) * progress

          // 计算透明度（开始和结束时透明，中间不透明）
          let opacity = 1
          if (progress < 0.1) {
            opacity = progress / 0.1
          } else if (progress > 0.9) {
            opacity = (1 - progress) / 0.1
          }

          dot.style.left = `${currentX - 4}px`
          dot.style.top = `${currentY - 4}px`
          dot.style.opacity = opacity

          requestAnimationFrame(animate)
        } else {
          // 动画结束，重新开始
          console.log("动画完成一轮，重新开始")
          setTimeout(() => {
            this.animateDot(dot, startX, startY, endX, endY, duration)
          }, 100)
        }
      }

      animate()
    },
    cleanupFlowDots() {
      const zoomContainer = this.$refs.zoomContainer
      if (!zoomContainer) return

      // 移除所有流动点
      const flowDots = zoomContainer.querySelectorAll('.flow-dot-html')
      flowDots.forEach(dot => {
        dot.remove()
      })
    },
    // createFlowingDots(svg, pathData) {
    //   const svgNS = "http://www.w3.org/2000/svg"
    //   const dotColor = this.option.flowDotColor || '#00ff88'

    //   console.log("创建流动点，路径数据:", pathData)

    //   // 为每条路径创建一个唯一的路径元素
    //   const pathId = `flow-path-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`
    //   const pathElement = document.createElementNS(svgNS, 'path')
    //   pathElement.setAttribute('id', pathId)
    //   pathElement.setAttribute('d', pathData)
    //   pathElement.setAttribute('fill', 'none')
    //   pathElement.setAttribute('stroke', 'none')
    //   svg.appendChild(pathElement)

    //   // 创建多个流动点
    //   for (let i = 0; i < 3; i++) {
    //     console.log("创建多个流动点:",i+1)
    //     const circle = document.createElementNS(svgNS, 'circle')
    //     circle.setAttribute('r', '4')
    //     circle.setAttribute('fill', dotColor)
    //     circle.setAttribute('opacity', '0')
    //     circle.style.filter = `drop-shadow(0 0 6px ${dotColor})`

    //     // 创建动画路径
    //     const animateMotion = document.createElementNS(svgNS, 'animateMotion')
    //     animateMotion.setAttribute('dur', '3s')
    //     animateMotion.setAttribute('repeatCount', 'indefinite')
    //     animateMotion.setAttribute('begin', `${i * 1}s`)

    //     // 使用mpath引用路径
    //     const mpath = document.createElementNS(svgNS, 'mpath')
    //     mpath.setAttributeNS('http://www.w3.org/1999/xlink', 'href', `#${pathId}`)
    //     animateMotion.appendChild(mpath)

    //     // 添加透明度动画
    //     const animateOpacity = document.createElementNS(svgNS, 'animate')
    //     animateOpacity.setAttribute('attributeName', 'opacity')
    //     animateOpacity.setAttribute('values', '0;1;1;0')
    //     animateOpacity.setAttribute('dur', '3s')
    //     animateOpacity.setAttribute('repeatCount', 'indefinite')
    //     animateOpacity.setAttribute('begin', `${i * 1}s`)

    //     circle.appendChild(animateMotion)
    //     circle.appendChild(animateOpacity)
    //     svg.appendChild(circle)
    //   }
    // },
    updateZoom() {
      const layout = this.$refs.deviceLayout
      const zoomContainer = this.$refs.zoomContainer
      if (!layout || !zoomContainer) return

      const layoutWidth = layout.clientWidth
      const layoutHeight = layout.clientHeight

      // 如果没有设备数据，使用默认尺寸
      if (!this.devices || this.devices.length === 0) {
        zoomContainer.style.width = `${layoutWidth}px`
        zoomContainer.style.height = `${layoutHeight}px`
        zoomContainer.style.transform = 'translate(0px, 0px) scale(1)'
        return
      }

      // 计算内容边界
      let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity
      this.devices.forEach(d => {
        minX = Math.min(minX, d.x)
        minY = Math.min(minY, d.y)
        maxX = Math.max(maxX, d.x + 120) // 120是设备宽度
        maxY = Math.max(maxY, d.y + 70)  // 70是设备高度
      })

      // 添加边距
      const padding = 50
      const contentWidth = maxX - minX + padding * 2
      const contentHeight = maxY - minY + padding * 2

      // 计算缩放比例
      const scaleX = layoutWidth / contentWidth
      const scaleY = layoutHeight / contentHeight
      const scale = Math.min(scaleX, scaleY, 1)

      // 计算偏移量，确保内容居中
      const scaledWidth = contentWidth * scale
      const scaledHeight = contentHeight * scale
      const offsetX = (layoutWidth - scaledWidth) / 2 - (minX - padding) * scale
      const offsetY = (layoutHeight - scaledHeight) / 2 - (minY - padding) * scale

      // 应用变换
      zoomContainer.style.width = `${contentWidth}px`
      zoomContainer.style.height = `${contentHeight}px`
      zoomContainer.style.transform = `translate(${offsetX}px, ${offsetY}px) scale(${scale})`

      // 存储当前缩放信息，供其他方法使用
      this.currentScale = scale
      this.currentOffset = { x: offsetX, y: offsetY }
    },
    updateTooltipPosition(event) {
      const layoutRect = this.$refs.deviceLayout.getBoundingClientRect()
      const mouseX = event.clientX
      const mouseY = event.clientY

      // tooltip的预估尺寸
      const tooltipWidth = 200
      const tooltipHeight = 100

      // 计算相对于设备布局容器的位置
      const relativeX = mouseX - layoutRect.left
      const relativeY = mouseY - layoutRect.top

      let tooltipX = relativeX + 15
      let tooltipY = relativeY + 15

      // 检查是否会超出右边界，如果是则显示在鼠标左侧
      if (tooltipX + tooltipWidth > layoutRect.width) {
        tooltipX = relativeX - tooltipWidth - 15
      }

      // 检查是否会超出下边界，如果是则显示在鼠标上方
      if (tooltipY + tooltipHeight > layoutRect.height) {
        tooltipY = relativeY - tooltipHeight - 15
      }

      // 确保不会超出左边界和上边界
      tooltipX = Math.max(10, tooltipX)
      tooltipY = Math.max(10, tooltipY)

      this.tooltipPosition = {
        x: tooltipX,
        y: tooltipY
      }
    },
    openDeviceModal(device) {
      this.currentDevice = device
      this.modalTitle = `${device.name} - 设备详情`
      this.activeTab = device.status === 'alarm' ? 'alarm' : 'params'
      this.showModal = true
    },
    openCameraModal(device) {
      this.cameraTitle = `${device.name} - 实时监控`
      this.currentCameraUrl = device.cameraUrl || ''
      this.showCameraModal = true

      // 如果是HLS流，延迟初始化播放器
      if (this.currentCameraUrl && this.isHlsUrl(this.currentCameraUrl)) {
        this.$nextTick(() => {
          this.initHlsPlayer()
        })
      }
    },
    closeModal() {
      this.showModal = false
      this.currentDevice = null
    },
    closeCameraModal() {
      this.destroyHlsPlayer()
      this.showCameraModal = false
      this.currentCameraUrl = ''
    },

    // HLS播放器相关方法
    isHlsUrl(url) {
      return url && (url.includes('.m3u8') || url.includes('hls'))
    },

    initHlsPlayer() {
      if (!this.currentCameraUrl || !this.isHlsUrl(this.currentCameraUrl)) return

      // 检查是否有Clappr播放器
      if (typeof Clappr !== 'undefined') {
        this.destroyHlsPlayer()

        const container = document.getElementById(this.hlsPlayerId)
        if (container) {
          this.hlsPlayer = new Clappr.Player({
            parentId: '#' + this.hlsPlayerId,
            source: this.currentCameraUrl,
            autoPlay: true,
            mute: true,
            height: '450px',
            width: '100%'
          })
        }
      } else {
        console.warn('Clappr播放器未加载，无法播放HLS流')
      }
    },

    destroyHlsPlayer() {
      if (this.hlsPlayer) {
        try {
          this.hlsPlayer.destroy()
        } catch (error) {
          console.warn('销毁HLS播放器时出错:', error)
        }
        this.hlsPlayer = null
      }
    },

    refreshCamera() {
      if (this.isHlsUrl(this.currentCameraUrl)) {
        this.initHlsPlayer()
      }
    },
    startPolling() {
      if (this.option.enablePolling !== false) {
        console.log("====startPolling=====")
        this.pollingTimer = setInterval(() => {
          this.updateDeviceStatuses()
        }, this.option.pollingInterval || 3000)
      }
    },
    async updateDeviceStatuses() {
      try {
        const newDevices = await this.getDeviceData()
        newDevices.forEach(newDevice => {
          const deviceElement = document.getElementById(newDevice.id)
          if (!deviceElement) return

          const currentDevice = this.devices.find(d => d.id === newDevice.id)
          if (currentDevice && currentDevice.status === newDevice.status) {
            return
          }

          // 更新状态
          deviceElement.classList.remove('running', 'idle', 'alarm')
          deviceElement.classList.add(newDevice.status)

          // 更新样式（保持原插件风格）
          deviceElement.style.backgroundColor = this.option.deviceBackgroundColor || '#0f3460'
          deviceElement.style.animation = ''

          if (newDevice.status === 'running') {
            deviceElement.style.borderColor = '#2ecc71'
            deviceElement.style.color = this.option.textColor || '#dcdcdc'
          } else if (newDevice.status === 'idle') {
            deviceElement.style.borderColor = '#3498db'
            deviceElement.style.color = this.option.textColor || '#dcdcdc'
          } else if (newDevice.status === 'alarm') {
            deviceElement.style.borderColor = '#e74c3c'
            deviceElement.style.color = this.option.textColor || '#dcdcdc'
            deviceElement.style.animation = 'pulse-alarm 1s infinite'
          }

          // 更新告警图标
          const alarmIcon = deviceElement.querySelector('.alarm-icon')
          if (alarmIcon) {
            alarmIcon.style.display = newDevice.status === 'alarm' ? 'inline-block' : 'none'
          }

          // 更新内存中的设备数据
          if (currentDevice) {
            currentDevice.status = newDevice.status
          }
        })
      } catch (error) {
        console.error('Error updating device statuses:', error)
      }
    },
    // 重新渲染组件（当配置变化时调用）
    refresh() {
      this.$nextTick(() => {
        this.renderDevices()
        this.updateZoom()
      })
    }
  },
  watch: {
    // 缩放BOX监听 刷新界面
    styleSizeName(){
      console.log("styleSizeName change")
       this.refresh()
    },
    // 监听配置变化，自动重新渲染
    'option.deviceBackgroundColor'() {
      this.refresh()
    },
    'option.connectionLineColor'() {
      this.refresh()
    },
    'option.backgroundOpacity'() {
      this.refresh()
    },
    'option.layoutBackgroundColor'() {
      this.refresh()
    },
    //启用光点效果
      'option.flowDotsEnabled'() {
      this.refresh()
    },
    //光点颜色
      'option.flowDotColor'() {
      this.refresh()
    },
     //光点速度
      'option.flowDotSpeed'() {
      this.refresh()
    },
      //光点数量
      'option.flowDotsNumber'() {
      this.refresh()
    },
       //文字大小
      'option.textSize'() {
      this.refresh()
    },
    
      'option.customDevices'() {
      this.refresh()
    },
    // 监听数据变化（动态数据源）
    data: {
      handler(newData) {
        if (newData && Array.isArray(newData)) {
          console.log("数据变化，重新渲染设备布局")
          this.initializeLayout()
          // this.startPolling()
          //  this.refresh()
          
        }
      },
      deep: true
    }
  }
}
</script>

<style scoped>
/* CSS变量定义 */
:root {
  --bg-color: #1a1a2e;
  --primary-color: #16213e;
  --secondary-color: #0f3460;
  --font-color: #e94560;
  --text-color: #dcdcdc;
  --border-color: #0f3460;
  --running-color: #2ecc71;
  --idle-color: #3498db;
  --alarm-color: #e74c3c;
  --arrow-color: #537895;
}

.equipment-monitoring-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-family: 'Noto Sans SC', sans-serif;
  /* 移除z-index避免创建层叠上下文 */
}

header {
  padding: 20px;
  text-align: center;
}

.equipment-header {
  padding: 20px;
  text-align: center;
}

.device-layout {
  position: relative;
  width: 100%;
  overflow: hidden;
  flex-grow: 1;
}

.zoom-container {
  position: relative;
  transform-origin: top left;
  transition: transform 0.3s ease-in-out;
}

.device {
  position: absolute;
  transition: all 0.3s ease;
  background-color: var(--secondary-color);
  border: 2px solid var(--idle-color);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
}

.device:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
}

.device.running {
  border-color: var(--running-color);
}

.device.alarm {
  border-color: var(--alarm-color);
  animation: pulse-alarm 1s infinite;
}

.device-name {
  font-weight: 500;
  font-size: 0.9rem;
}

.modal {
  display: block;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: #0f3460;
  backdrop-filter: blur(5px);
}

.modal-content {
  background-color: var(--primary-color);
  margin: 15% auto;
  padding: 20px;
  border: 1px solid var(--border-color);
  width: 80%;
  max-width: 600px;
  border-radius: 10px;
  position: relative;
  animation: slide-in 0.5s ease-out;
  color: var(--text-color);
}

.camera-view {
  max-width: 850px;
}

.close-button {
  color: #aaa;
  float: right;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close-button:hover,
.close-button:focus {
  color: var(--text-color);
  text-decoration: none;
}

.modal h2 {
  color: var(--font-color);
  margin-bottom: 20px;
}

.tabs {
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 20px;
}

.tab-link {
  background: none;
  border: none;
  padding: 10px 20px;
  cursor: pointer;
  color: var(--text-color);
  font-size: 1rem;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.tab-link.active,
.tab-link:hover {
  opacity: 1;
  border-bottom: 2px solid var(--font-color);
}

.tab-content {
  display: none;
  animation: fade-in 0.5s;
}

.tab-content.active {
  display: block;
}

.tab-content p {
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.tab-content p span {
  color: var(--running-color);
  font-weight: 500;
}

.device-tooltip {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  pointer-events: none;
  z-index: 1000;
  font-size: 12px;
}

/* 脉冲告警动画 */
@keyframes pulse-alarm {
  0% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
  }
}

/* 流动点样式 - HTML+CSS动画实现 */
.flow-dot-html {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  pointer-events: none;
  z-index: 10;
  transition: opacity 0.1s ease;
}

/* 连接线样式 */
.connector {
  stroke: var(--arrow-color);
  stroke-width: 2;
  fill: none;
}

/* 模态窗口动画 */
@keyframes slide-in {
  from {
    top: -100px;
    opacity: 0;
  }
  to {
    top: 0;
    opacity: 1;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}



.camera-container {
  width: 100%;
  min-height: 450px;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hls-player-container {
  width: 100%;
  height: 450px;
  background: #000;
}

.video-player {
  width: 100%;
  height: 450px;
  object-fit: cover;
}

.camera-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 450px;
  color: var(--text-color);
  opacity: 0.6;
}

.placeholder-icon {
  margin-bottom: 20px;
}

.camera-placeholder p {
  font-size: 16px;
  margin: 0;
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

/* 设备图标样式优化 */
.device-icons {
  pointer-events: auto;
}

.camera-btn {
  cursor: pointer !important;
  transition: all 0.3s ease;
  pointer-events: auto;
}

.camera-btn:hover {
  opacity: 1 !important;
  transform: scale(1.2);
  filter: drop-shadow(0 0 8px #3498db);
}

.alarm-icon {
  pointer-events: none;
}

/* 设备容器优化 */
.device {
  pointer-events: auto;
}

.device-name {
  pointer-events: none;
}
</style>

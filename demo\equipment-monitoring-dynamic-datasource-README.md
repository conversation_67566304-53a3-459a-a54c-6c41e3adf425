# 设备监控台控件 - 动态数据源功能

## 概述

设备监控台控件现在支持动态数据源功能，通过使用 `create` 函数集成 `common` mixin，自动获得了完整的数据源处理能力。

## 支持的数据源类型

### 1. 静态数据 (dataType: 0)
直接在配置中定义设备数据。

```javascript
{
  dataType: 0,
  data: [
    {
      id: 'dev-01',
      name: '设备1',
      x: 100,
      y: 100,
      status: 'running',
      connections: ['dev-02'],
      icons: { camera: true, star: false },
      tooltipData: { '型号': 'MODEL-001', '厂商': 'Company A' }
    }
  ]
}
```

### 2. API接口数据 (dataType: 1)
通过HTTP请求获取设备数据。

```javascript
{
  dataType: 1,
  dataMethod: 'get',
  url: '/api/equipment/devices',
  time: 5000, // 轮询间隔（毫秒）
  dataQuery: `(url) => ({
    page: 1,
    size: 20,
    filters: { status: 'active' }
  })`,
  dataHeader: `(url) => ({
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + localStorage.getItem('token')
  })`,
  dataFormatter: `(data, params, refs) => {
    return data.map((item, index) => ({
      id: item.deviceId || 'dev-' + index,
      name: item.deviceName || '设备' + index,
      x: item.positionX || index * 150 + 50,
      y: item.positionY || 50,
      status: item.status || 'running',
      connections: item.connections || [],
      icons: {
        camera: item.hasCamera || false,
        star: item.isImportant || false
      },
      tooltipData: item.details || {}
    }));
  }`
}
```

### 3. 自定义数据接口 (dataType: 5)
使用预定义的数据集ID获取数据。

```javascript
{
  dataType: 5,
  dataSet: 'DATASET_ID_123',
  time: 10000,
  dataFormatter: `(data, params, refs) => {
    // 数据格式化逻辑
    return data;
  }`
}
```

### 4. 全局数据源 (dataType: 6)
使用全局缓存的数据源，支持多组件间数据共享。

```javascript
{
  dataType: 6,
  dataSet: 'GLOBAL_DATASET_ID',
  time: 30000
}
```

## 数据格式要求

设备数据必须符合以下格式：

```javascript
{
  id: string,           // 必需：设备唯一标识
  name: string,         // 必需：设备名称
  x: number,           // 必需：X坐标位置
  y: number,           // 必需：Y坐标位置
  status: string,      // 可选：设备状态 ('running', 'idle', 'alarm')
  connections: array,  // 可选：连接的设备ID数组
  icons: {             // 可选：图标配置
    camera: boolean,   // 是否显示摄像头图标
    star: boolean      // 是否显示星标图标
  },
  tooltipData: object  // 可选：鼠标悬停显示的详细信息
}
```

## 使用示例

### 基本用法

```html
<template>
  <equipment-monitoring 
    :option="option"
    :data-type="dataType"
    :data-method="dataMethod"
    :url="url"
    :time="time"
    :data-query="dataQuery"
    :data-header="dataHeader"
    :data-formatter="dataFormatter"
    :data="data"
  />
</template>

<script>
export default {
  data() {
    return {
      option: {
        showHeader: true,
        title: '设备监控台',
        backgroundColor: '#1a1a2e',
        // ... 其他样式配置
      },
      dataType: 1,
      dataMethod: 'get',
      url: '/api/equipment/devices',
      time: 5000,
      dataQuery: '(url) => ({ status: "active" })',
      dataHeader: '(url) => ({ "Content-Type": "application/json" })',
      dataFormatter: this.formatDeviceData,
      data: []
    }
  },
  methods: {
    formatDeviceData(data, params, refs) {
      return data.map(item => ({
        id: item.deviceId,
        name: item.deviceName,
        x: item.positionX,
        y: item.positionY,
        status: item.status,
        connections: item.connections || [],
        icons: { camera: item.hasCamera, star: false },
        tooltipData: item.details || {}
      }));
    }
  }
}
</script>
```

### 配置界面集成

在配置组件中，已经添加了完整的数据源配置界面：

- 数据源类型选择
- API接口配置（URL、请求方式、参数、请求头）
- 数据格式化函数编辑器
- 轮询间隔设置

## 测试案例

运行 `demo/equipment-monitoring-dynamic-datasource-test.html` 查看完整的动态数据源功能演示。

## 注意事项

1. **数据格式化函数**：必须返回符合设备数据格式的数组
2. **轮询间隔**：建议不要设置过短的间隔，避免频繁请求
3. **错误处理**：API请求失败时会自动回退到默认数据
4. **性能优化**：全局数据源会自动缓存数据，减少重复请求

## 技术实现

- 使用 `create` 函数集成 `common` mixin
- 自动获得完整的数据源处理能力
- 支持数据格式化、错误处理、轮询等功能
- 与现有配置系统完全兼容
